# 分布式Agent系统上下文管理方案

## 1. 上下文管理架构

### 1.1 上下文分层设计
```
┌─────────────────────────────────────────────────────────────┐
│                    Context Management Layer                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Session     │  │ Agent       │  │ Global      │         │
│  │ Context     │  │ Context     │  │ Context     │         │
│  │ (短期记忆)   │  │ (中期记忆)   │  │ (长期记忆)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Redis       │  │ PostgreSQL  │  │ Elasticsearch│         │
│  │ (热缓存)     │  │ (持久化)     │  │ (检索引擎)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 上下文类型定义

#### Session Context (会话上下文)
- **生命周期**: 单次会话
- **存储位置**: Redis (TTL: 30分钟)
- **内容**: 当前对话历史、临时状态、工具调用结果

#### Agent Context (Agent上下文)
- **生命周期**: Agent实例级别
- **存储位置**: PostgreSQL + Redis缓存
- **内容**: Agent配置、工具列表、知识库索引

#### Global Context (全局上下文)
- **生命周期**: 跨会话持久化
- **存储位置**: Elasticsearch
- **内容**: 用户偏好、历史交互模式、知识图谱

## 2. 核心组件设计

### 2.1 Context Service (上下文服务)
```java
@Service
public class DistributedContextService {
    
    // 会话上下文管理
    Mono<SessionContext> getSessionContext(String sessionId);
    Mono<Void> updateSessionContext(String sessionId, SessionContext context);
    
    // Agent上下文管理
    Mono<AgentContext> getAgentContext(Long agentId);
    Mono<Void> cacheAgentContext(Long agentId, AgentContext context);
    
    // 全局上下文管理
    Mono<GlobalContext> getGlobalContext(Long userId);
    Mono<Void> updateGlobalContext(Long userId, GlobalContext context);
    
    // 上下文合并
    Mono<MergedContext> mergeContexts(String sessionId, Long agentId, Long userId);
}
```

### 2.2 Context Cache Manager (上下文缓存管理器)
```java
@Component
public class ContextCacheManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    
    // 分布式锁确保上下文一致性
    public <T> Mono<T> withDistributedLock(String lockKey, Supplier<Mono<T>> operation) {
        return Mono.fromCallable(() -> {
            String lockValue = UUID.randomUUID().toString();
            Boolean acquired = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(30));
            
            if (acquired) {
                try {
                    return operation.get().block();
                } finally {
                    // 释放锁
                    String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                                   "return redis.call('del', KEYS[1]) else return 0 end";
                    redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                                        Collections.singletonList(lockKey), lockValue);
                }
            } else {
                throw new RuntimeException("获取分布式锁失败: " + lockKey);
            }
        });
    }
}
```

## 3. 上下文传递机制

### 3.1 服务间上下文传递
```java
// 使用HTTP Header传递上下文ID
@Component
public class ContextPropagationInterceptor implements ClientHttpRequestInterceptor {
    
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 从当前线程获取上下文
        String sessionId = ContextHolder.getSessionId();
        String agentId = ContextHolder.getAgentId();
        String userId = ContextHolder.getUserId();
        
        // 添加到请求头
        request.getHeaders().add("X-Session-Id", sessionId);
        request.getHeaders().add("X-Agent-Id", agentId);
        request.getHeaders().add("X-User-Id", userId);
        
        return execution.execute(request, body);
    }
}
```

### 3.2 消息队列上下文传递
```java
@Component
public class ContextAwareMessageProducer {
    
    public void sendWithContext(String topic, Object message) {
        // 构建包含上下文的消息
        ContextMessage contextMessage = ContextMessage.builder()
            .sessionId(ContextHolder.getSessionId())
            .agentId(ContextHolder.getAgentId())
            .userId(ContextHolder.getUserId())
            .payload(message)
            .timestamp(System.currentTimeMillis())
            .build();
            
        rocketMQTemplate.convertAndSend(topic, contextMessage);
    }
}
```

## 4. 上下文一致性保证

### 4.1 分布式事务支持
```java
@Service
@Transactional
public class ContextTransactionService {
    
    @GlobalTransactional(rollbackFor = Exception.class)
    public Mono<Void> updateContextWithTransaction(
            String sessionId, 
            SessionContext sessionContext,
            Long agentId,
            AgentContext agentContext) {
        
        return Mono.fromRunnable(() -> {
            // 更新会话上下文
            contextCacheManager.updateSessionContext(sessionId, sessionContext);
            
            // 更新Agent上下文
            contextCacheManager.updateAgentContext(agentId, agentContext);
            
            // 如果任何一步失败，Seata会自动回滚
        });
    }
}
```

### 4.2 最终一致性处理
```java
@Component
public class ContextEventualConsistencyHandler {
    
    @EventListener
    public void handleContextUpdateEvent(ContextUpdateEvent event) {
        // 异步同步到其他存储
        CompletableFuture.runAsync(() -> {
            try {
                // 同步到Elasticsearch
                elasticsearchService.updateContext(event.getContextData());
                
                // 同步到其他缓存节点
                contextReplicationService.replicate(event);
                
            } catch (Exception e) {
                // 记录失败，稍后重试
                contextSyncRetryService.scheduleRetry(event);
            }
        });
    }
}
```

## 5. 性能优化策略

### 5.1 上下文预加载
```java
@Component
public class ContextPreloader {
    
    @Async
    public CompletableFuture<Void> preloadAgentContext(Long agentId) {
        return CompletableFuture.runAsync(() -> {
            // 预加载Agent配置
            AgentContext context = buildAgentContext(agentId);
            
            // 缓存到Redis
            contextCacheManager.cacheAgentContext(agentId, context);
        });
    }
}
```

### 5.2 上下文压缩存储
```java
@Component
public class ContextCompressionService {
    
    public String compressContext(Object context) {
        try {
            // 序列化
            byte[] serialized = objectMapper.writeValueAsBytes(context);
            
            // GZIP压缩
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
                gzipOut.write(serialized);
            }
            
            // Base64编码
            return Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("上下文压缩失败", e);
        }
    }
}
```

## 6. 监控与调试

### 6.1 上下文链路追踪
```java
@Component
public class ContextTracing {
    
    public void traceContextFlow(String operation, String contextId) {
        Span span = tracer.nextSpan()
            .name("context-operation")
            .tag("operation", operation)
            .tag("context.id", contextId)
            .tag("service.name", "context-service")
            .start();
            
        try (Tracer.SpanInScope ws = tracer.withSpanInScope(span)) {
            // 执行操作
        } finally {
            span.end();
        }
    }
}
```

### 6.2 上下文健康检查
```java
@Component
public class ContextHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查Redis连接
            redisTemplate.opsForValue().get("health-check");
            
            // 检查数据库连接
            contextRepository.count();
            
            // 检查Elasticsearch连接
            elasticsearchTemplate.indexExists("context-index");
            
            return Health.up()
                .withDetail("redis", "UP")
                .withDetail("database", "UP")
                .withDetail("elasticsearch", "UP")
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```
