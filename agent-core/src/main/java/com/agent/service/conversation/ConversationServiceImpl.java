package com.agent.service.conversation;

import com.agent.domain.entity.Agent;
import com.agent.domain.entity.Conversation;
import com.agent.domain.entity.Message;
import com.agent.repository.AgentRepository;
import com.agent.repository.ConversationRepository;
import com.agent.repository.MessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 会话服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConversationServiceImpl implements ConversationService {
    
    private final ConversationRepository conversationRepository;
    private final MessageRepository messageRepository;
    private final AgentRepository agentRepository;
    
    @Override
    public Mono<Conversation> getOrCreateConversation(Long agentId, Long userId, String conversationId) {
        if (conversationId != null) {
            // 尝试获取现有会话
            return Mono.fromCallable(() -> conversationRepository.findByUuid(conversationId))
                    .flatMap(optional -> {
                        if (optional.isPresent()) {
                            return Mono.just(optional.get());
                        } else {
                            // 会话不存在，创建新会话
                            return createNewConversation(agentId, userId);
                        }
                    });
        } else {
            // 创建新会话
            return createNewConversation(agentId, userId);
        }
    }
    
    @Override
    public Mono<Message> saveMessage(String conversationId, String role, String content) {
        return Mono.fromCallable(() -> conversationRepository.findByUuid(conversationId))
                .flatMap(optional -> {
                    if (optional.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("会话不存在: " + conversationId));
                    }

                    Conversation conversation = optional.get();

                    Message message = new Message();
                    message.setUuid(UUID.randomUUID().toString());
                    message.setConversation(conversation);
                    message.setRole(Message.MessageRole.valueOf(role.toUpperCase()));
                    message.setContent(content);
                    message.setCreatedAt(LocalDateTime.now());

                    return Mono.fromCallable(() -> messageRepository.save(message))
                            .flatMap(savedMessage -> {
                                // 更新会话的最后消息和活跃时间
                                conversation.setLastMessage(content);
                                conversation.setLastActiveAt(LocalDateTime.now());
                                conversation.setUpdatedAt(LocalDateTime.now());

                                // 更新消息数量
                                Integer currentCount = conversation.getMessageCount();
                                conversation.setMessageCount(currentCount != null ? currentCount + 1 : 1);

                                // 如果是第一条用户消息，更新会话标题
                                if (role.equalsIgnoreCase("user") &&
                                    (conversation.getTitle() == null || conversation.getTitle().equals("新对话"))) {
                                    // 使用消息内容的前20个字符作为标题
                                    String title = content.length() > 20 ? content.substring(0, 20) + "..." : content;
                                    conversation.setTitle(title);
                                }

                                return Mono.fromCallable(() -> conversationRepository.save(conversation))
                                        .thenReturn(savedMessage);
                            });
                })
                .doOnSuccess(message -> log.debug("保存消息成功: {}", message.getId()));
    }
    
    @Override
    public Mono<List<Message>> getConversationHistory(String conversationId, int limit) {
        return Mono.fromCallable(() -> 
                messageRepository.findByConversationUuidOrderByCreatedAtAsc(conversationId)
        );
    }
    
    @Override
    public Mono<List<Conversation>> getUserConversations(Long userId, Long agentId) {
        return Mono.fromCallable(() -> 
                conversationRepository.findByUserIdAndAgentIdOrderByLastActiveAtDesc(userId, agentId)
        );
    }
    
    private Mono<Conversation> createNewConversation(Long agentId, Long userId) {
        return Mono.fromCallable(() -> agentRepository.findById(agentId))
                .flatMap(optional -> {
                    if (optional.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("Agent不存在: " + agentId));
                    }
                    
                    Agent agent = optional.get();
                    
                    Conversation conversation = new Conversation();
                    conversation.setUuid(UUID.randomUUID().toString());
                    conversation.setAgent(agent);
                    conversation.setUserId(userId);
                    conversation.setTitle("新对话");
                    conversation.setStatus(Conversation.ConversationStatus.ACTIVE);
                    conversation.setMessageCount(0);
                    conversation.setLastActiveAt(LocalDateTime.now());
                    conversation.setCreatedAt(LocalDateTime.now());
                    conversation.setUpdatedAt(LocalDateTime.now());
                    
                    return Mono.fromCallable(() -> conversationRepository.save(conversation));
                })
                .doOnSuccess(conversation -> log.info("创建新会话: {}", conversation.getUuid()));
    }
}
