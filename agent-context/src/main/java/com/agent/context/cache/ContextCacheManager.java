package com.agent.context.cache;

import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

/**
 * 上下文缓存管理器接口
 */
public interface ContextCacheManager {

    /**
     * 从缓存获取上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 上下文数据
     */
    Mono<ContextData> get(String contextId, ContextType type);

    /**
     * 将上下文数据放入缓存
     *
     * @param contextData 上下文数据
     * @param ttl         过期时间
     * @return 操作结果
     */
    Mono<Void> put(ContextData contextData, Duration ttl);

    /**
     * 从缓存删除上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 操作结果
     */
    Mono<Void> delete(String contextId, ContextType type);

    /**
     * 批量获取上下文数据
     *
     * @param contextIds 上下文ID列表
     * @param type       上下文类型
     * @return 上下文数据列表
     */
    Flux<ContextData> multiGet(List<String> contextIds, ContextType type);

    /**
     * 批量放入缓存
     *
     * @param contextDataList 上下文数据列表
     * @param ttl             过期时间
     * @return 操作结果
     */
    Mono<Void> multiPut(List<ContextData> contextDataList, Duration ttl);

    /**
     * 检查缓存中是否存在指定上下文
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 是否存在
     */
    Mono<Boolean> exists(String contextId, ContextType type);

    /**
     * 设置上下文过期时间
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @param ttl       过期时间
     * @return 操作结果
     */
    Mono<Void> expire(String contextId, ContextType type, Duration ttl);

    /**
     * 获取上下文剩余过期时间
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 剩余过期时间
     */
    Mono<Duration> getTtl(String contextId, ContextType type);

    /**
     * 清理指定类型的所有过期上下文
     *
     * @param type 上下文类型
     * @return 清理的数量
     */
    Mono<Long> cleanupExpired(ContextType type);

    /**
     * 获取缓存统计信息
     *
     * @param type 上下文类型
     * @return 统计信息
     */
    Mono<CacheStats> getStats(ContextType type);

    /**
     * 使用分布式锁执行操作
     *
     * @param lockKey   锁键
     * @param timeout   锁超时时间
     * @param operation 要执行的操作
     * @param <T>       返回类型
     * @return 操作结果
     */
    <T> Mono<T> withDistributedLock(String lockKey, Duration timeout, Supplier<Mono<T>> operation);

    /**
     * 预热缓存
     *
     * @param contextIds 要预热的上下文ID列表
     * @param type       上下文类型
     * @return 预热结果
     */
    Mono<Void> warmup(List<String> contextIds, ContextType type);

    /**
     * 缓存统计信息
     */
    interface CacheStats {
        long getHitCount();
        long getMissCount();
        double getHitRate();
        long getEvictionCount();
        long getSize();
        long getMemoryUsage();
    }
}
