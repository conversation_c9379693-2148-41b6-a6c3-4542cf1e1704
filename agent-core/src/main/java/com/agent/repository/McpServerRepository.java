package com.agent.repository;

import com.agent.domain.entity.McpServer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MCP Server Repository
 */
@Repository
public interface McpServerRepository extends JpaRepository<McpServer, Long> {
    
    /**
     * 根据创建者ID查找MCP Server
     */
    List<McpServer> findByCreatorIdOrderByCreatedAtDesc(Long creatorId);
    
    /**
     * 根据名称和创建者ID查找MCP Server
     */
    McpServer findByNameAndCreatorId(String name, Long creatorId);
    
    /**
     * 查找启用的MCP Server
     */
    List<McpServer> findByEnabledTrueOrderByCreatedAtDesc();
    
    /**
     * 根据状态查找MCP Server
     */
    List<McpServer> findByStatusOrderByCreatedAtDesc(McpServer.McpServerStatus status);
}
