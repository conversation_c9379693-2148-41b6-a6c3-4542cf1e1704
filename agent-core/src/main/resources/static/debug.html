<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            display: flex;
            gap: 20px;
        }
        .test-section {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .output {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            overflow-y: auto;
            max-height: 400px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .chat-output {
            background: white;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 3px;
            min-height: 100px;
            font-size: 14px;
        }
        .streaming {
            border-left: 3px solid #007bff;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <h1>前端调试页面</h1>

    <div style="background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 5px;">
        <h3>🔧 调试说明</h3>
        <p><strong>如果聊天界面测试没有显示内容，请检查浏览器控制台（F12）查看详细日志。</strong></p>
        <p>我们已经添加了详细的调试信息来帮助定位问题。</p>
    </div>

    <div class="test-container">
        <!-- 左侧：原始数据调试 -->
        <div class="test-section">
            <h3>原始数据调试</h3>
            <button onclick="testRawData()">测试原始数据</button>
            <button onclick="clearDebug()">清空</button>
            <div id="debug-output" class="output"></div>
        </div>

        <!-- 右侧：纯Markdown聊天测试 -->
        <div class="test-section">
            <h3>🚀 纯Markdown聊天测试 (无JSON解析)</h3>
            <input type="text" id="chatInput" placeholder="输入消息..." style="width: 100%; padding: 8px; margin-bottom: 10px;" />
            <button onclick="sendPureMarkdownMessage()">发送消息</button>
            <button onclick="clearChat()">清空</button>
            <div id="chat-output" class="chat-output"></div>
        </div>
    </div>

    <script>
        // 测试原始数据
        async function testRawData() {
            const output = document.getElementById('debug-output');
            output.textContent = '开始测试原始数据...\n';

            try {
                const response = await fetch('/v1/agents/7/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好',
                        userId: 1,
                        stream: true
                    })
                });

                output.textContent += `HTTP状态: ${response.status} ${response.statusText}\n`;
                output.textContent += `Content-Type: ${response.headers.get('content-type')}\n\n`;

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunkCount = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunkCount++;
                    const chunk = decoder.decode(value, { stream: true });
                    output.textContent += `--- 数据块 ${chunkCount} ---\n`;
                    output.textContent += `原始数据: ${JSON.stringify(chunk)}\n`;
                    
                    const lines = chunk.split('\n');
                    output.textContent += `分割后行数: ${lines.length}\n`;
                    
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];
                        output.textContent += `行${i}: ${JSON.stringify(line)}\n`;
                        
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            output.textContent += `  提取数据: ${JSON.stringify(data)}\n`;
                            
                            if (data === '[DONE]') {
                                output.textContent += '  ✅ 检测到结束标记\n';
                                return;
                            }
                            
                            // 现在是纯Markdown格式，不需要JSON解析
                            if (data && data.trim() !== '') {
                                output.textContent += `  ✅ Markdown数据: "${data}"\n`;
                            } else {
                                output.textContent += `  ℹ️ 空数据或换行\n`;
                            }
                        }
                    }
                    output.textContent += '\n';
                    
                    // 自动滚动到底部
                    output.scrollTop = output.scrollHeight;
                }
            } catch (error) {
                output.textContent += `❌ 错误: ${error.message}\n`;
            }
        }

        // 测试Markdown聊天界面
        async function testMarkdownChat() {
            const output = document.getElementById('chat-output');
            
            // 添加用户消息
            const userDiv = document.createElement('div');
            userDiv.style.marginBottom = '10px';
            userDiv.style.padding = '8px';
            userDiv.style.backgroundColor = '#e3f2fd';
            userDiv.style.borderRadius = '5px';
            userDiv.textContent = '用户: 你好';
            output.appendChild(userDiv);

            // 添加助手消息容器
            const assistantDiv = document.createElement('div');
            assistantDiv.style.marginBottom = '10px';
            assistantDiv.style.padding = '8px';
            assistantDiv.style.backgroundColor = '#f5f5f5';
            assistantDiv.style.borderRadius = '5px';
            assistantDiv.className = 'streaming';
            assistantDiv.textContent = '助手: ';
            output.appendChild(assistantDiv);

            let fullContent = '';

            try {
                console.log('开始发送请求...');
                const response = await fetch('/v1/agents/7/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好',
                        userId: 1,
                        stream: true
                    })
                });

                console.log('收到响应:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                console.log('开始读取流数据...');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        console.log('流读取完成');
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    console.log('收到数据块:', JSON.stringify(chunk));
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        console.log('处理行:', JSON.stringify(line));
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            console.log('提取数据:', JSON.stringify(data));

                            if (data === '[DONE]') {
                                console.log('检测到结束标记');
                                assistantDiv.classList.remove('streaming');
                                console.log('流式传输完成，最终内容:', fullContent);
                                return;
                            }

                            // 处理纯Markdown格式数据
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                assistantDiv.textContent = '助手: ' + fullContent;
                                output.scrollTop = output.scrollHeight;
                                console.log('更新内容，当前总长度:', fullContent.length);
                            }
                        }
                    }
                }

                // 流结束后的最终处理
                assistantDiv.classList.remove('streaming');
                console.log('所有数据处理完成，最终内容长度:', fullContent.length);
            } catch (error) {
                assistantDiv.classList.remove('streaming');
                assistantDiv.textContent = '助手: ❌ 错误: ' + error.message;
                assistantDiv.style.backgroundColor = '#ffebee';
            }
        }

        function clearDebug() {
            document.getElementById('debug-output').textContent = '';
        }

        function clearChat() {
            document.getElementById('chat-output').innerHTML = '';
        }

        // 纯Markdown聊天功能 - 无JSON解析
        async function sendPureMarkdownMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) {
                alert('请输入消息');
                return;
            }

            const output = document.getElementById('chat-output');

            // 添加用户消息
            const userDiv = document.createElement('div');
            userDiv.style.marginBottom = '10px';
            userDiv.style.padding = '8px';
            userDiv.style.backgroundColor = '#e3f2fd';
            userDiv.style.borderRadius = '5px';
            userDiv.textContent = '用户: ' + message;
            output.appendChild(userDiv);

            // 添加AI消息容器
            const aiDiv = document.createElement('div');
            aiDiv.style.marginBottom = '10px';
            aiDiv.style.padding = '8px';
            aiDiv.style.backgroundColor = '#f5f5f5';
            aiDiv.style.borderRadius = '5px';
            aiDiv.style.borderLeft = '3px solid #2196f3';
            aiDiv.innerHTML = 'AI: <em>正在思考...</em>';
            output.appendChild(aiDiv);

            input.value = '';
            let fullContent = '';

            try {
                console.log('🚀 发送纯Markdown请求...');

                const response = await fetch('/v1/agents/7/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: message,
                        userId: 1,
                        stream: true
                    })
                });

                console.log('📡 响应状态:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                console.log('📥 开始读取流数据...');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        console.log('✅ 流读取完成');
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    console.log('📦 收到数据块:', chunk);

                    const lines = chunk.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            console.log('📄 提取数据:', data);

                            if (data === '[DONE]') {
                                console.log('🏁 检测到结束标记');
                                aiDiv.style.borderLeft = 'none';
                                return;
                            }

                            // 纯Markdown处理 - 绝对不使用JSON.parse
                            if (data && data.trim() !== '') {
                                fullContent += data;

                                // 使用marked渲染Markdown
                                if (typeof marked !== 'undefined' && marked.parse) {
                                    try {
                                        const rendered = marked.parse(fullContent);
                                        aiDiv.innerHTML = 'AI: ' + rendered;
                                        console.log('✅ Markdown渲染成功');
                                    } catch (error) {
                                        console.error('❌ Markdown渲染失败:', error);
                                        aiDiv.textContent = 'AI: ' + fullContent;
                                    }
                                } else {
                                    aiDiv.textContent = 'AI: ' + fullContent;
                                    console.warn('⚠️ marked库未加载');
                                }

                                output.scrollTop = output.scrollHeight;
                                console.log('📝 累积内容长度:', fullContent.length);
                            }
                        }
                    }
                }

                // 最终处理
                aiDiv.style.borderLeft = 'none';
                console.log('🎉 对话完成，最终长度:', fullContent.length);

            } catch (error) {
                console.error('❌ 错误:', error);
                aiDiv.innerHTML = 'AI: ❌ 错误: ' + error.message;
                aiDiv.style.backgroundColor = '#ffebee';
                aiDiv.style.borderLeft = 'none';
            }
        }

        // 回车发送消息
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendPureMarkdownMessage();
                    }
                });
            }
        });
    </script>
</body>
</html>
