# Agent Context Module

Agent V4 的分布式上下文管理模块，提供高性能、可扩展的上下文存储和管理功能。

## 🎯 **功能特性**

### **多层次上下文管理**
- **Session Context** - 会话级上下文（30分钟TTL）
- **Agent Context** - Agent级上下文（1小时TTL）  
- **Tool Context** - 工具调用上下文（10分钟TTL）
- **Knowledge Context** - 知识库上下文（15分钟TTL）
- **Global Context** - 全局持久化上下文（30天TTL）

### **分布式缓存支持**
- Redis集群支持
- 分布式锁机制
- 缓存预热和统计
- 自动过期清理

### **数据压缩优化**
- GZIP压缩算法
- 可配置压缩阈值
- 自动压缩/解压缩
- 压缩比统计

### **高可用特性**
- 多存储后端支持
- 故障自动恢复
- 性能监控指标
- 链路追踪支持

## 🏗️ **架构设计**

```
┌─────────────────────────────────────────────────────────────┐
│                    Context Service Layer                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Cache       │  │ Storage     │  │ Compression │         │
│  │ Manager     │  │ Service     │  │ Service     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Redis       │  │ PostgreSQL  │  │ Elasticsearch│         │
│  │ Cluster     │  │ Database    │  │ (可选)       │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 **快速开始**

### **1. 添加依赖**

在主项目的 `pom.xml` 中添加：

```xml
<dependency>
    <groupId>com.agent</groupId>
    <artifactId>agent-context</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. 配置文件**

在 `application.yml` 中添加配置：

```yaml
agent:
  context:
    session-context:
      ttl: PT30M  # 30分钟
      max-messages: 100
      compression-enabled: true
    
    cache:
      key-prefix: "agent:context:"
      lock-timeout: PT30S
      retry-attempts: 3
    
    compression:
      threshold: 1024  # 1KB
      algorithm: gzip
      level: 6

spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### **3. 使用示例**

```java
@Service
@RequiredArgsConstructor
public class MyService {
    
    private final ContextService contextService;
    
    public void saveSessionContext(String sessionId, Map<String, Object> data) {
        ContextData contextData = ContextData.builder()
                .contextId(sessionId)
                .type(ContextType.SESSION)
                .entityId(sessionId)
                .content(data)
                .build();
        
        contextService.saveContext(contextData)
                .subscribe(saved -> log.info("上下文已保存: {}", saved.getContextId()));
    }
    
    public void getSessionContext(String sessionId) {
        contextService.getContext(sessionId, ContextType.SESSION)
                .subscribe(context -> {
                    if (context != null) {
                        log.info("找到上下文: {}", context.getContent());
                    }
                });
    }
}
```

## ⚙️ **配置说明**

### **上下文类型配置**

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `session-context.ttl` | PT30M | 会话上下文TTL |
| `session-context.max-messages` | 100 | 最大消息数量 |
| `agent-context.cache-ttl` | PT1H | Agent上下文缓存TTL |
| `global-context.elasticsearch-enabled` | false | 是否启用ES存储 |

### **缓存配置**

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `cache.key-prefix` | "agent:context:" | 缓存键前缀 |
| `cache.lock-timeout` | PT30S | 分布式锁超时 |
| `cache.retry-attempts` | 3 | 重试次数 |

### **压缩配置**

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `compression.threshold` | 1024 | 压缩阈值（字节） |
| `compression.algorithm` | gzip | 压缩算法 |
| `compression.level` | 6 | 压缩级别 |

## 🔧 **高级功能**

### **分布式锁使用**

```java
contextCacheManager.withDistributedLock(
    "context-lock:" + contextId,
    Duration.ofSeconds(30),
    () -> {
        // 在分布式锁保护下执行操作
        return contextService.updateContext(contextId, type, newContent);
    }
).subscribe();
```

### **批量操作**

```java
// 批量获取
List<String> contextIds = Arrays.asList("ctx1", "ctx2", "ctx3");
contextService.getContexts(contextIds, ContextType.SESSION)
    .collectList()
    .subscribe(contexts -> log.info("获取到 {} 个上下文", contexts.size()));

// 批量缓存
List<ContextData> contextList = buildContextList();
contextCacheManager.multiPut(contextList, Duration.ofMinutes(30))
    .subscribe();
```

### **上下文搜索**

```java
Map<String, Object> query = Map.of(
    "userId", "user123",
    "status", "active"
);

contextService.searchContexts(query, ContextType.GLOBAL)
    .collectList()
    .subscribe(results -> log.info("搜索到 {} 个匹配的上下文", results.size()));
```

## 📊 **监控指标**

模块提供以下监控指标：

- **缓存命中率** - 缓存性能指标
- **压缩比率** - 存储优化效果
- **操作延迟** - 性能监控
- **错误率** - 可靠性指标

## 🧪 **测试**

运行单元测试：

```bash
mvn test -pl agent-context
```

运行集成测试（需要Redis）：

```bash
mvn verify -pl agent-context -Dspring.profiles.active=test
```

## 📝 **开发指南**

### **扩展存储后端**

实现 `ContextStorageService` 接口：

```java
@Service
public class CustomStorageService implements ContextStorageService {
    // 实现自定义存储逻辑
}
```

### **自定义压缩算法**

实现 `ContextCompressionService` 接口：

```java
@Service
public class CustomCompressionService implements ContextCompressionService {
    // 实现自定义压缩逻辑
}
```

## 🤝 **贡献指南**

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 **许可证**

MIT License
