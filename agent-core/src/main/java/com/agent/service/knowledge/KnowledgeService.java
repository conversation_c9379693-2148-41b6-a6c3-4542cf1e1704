package com.agent.service.knowledge;

import com.agent.domain.dto.AgentExecutionResponse;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 知识库服务接口
 */
public interface KnowledgeService {
    
    /**
     * 搜索知识库
     */
    Mono<String> search(Long agentId, String query);
    
    /**
     * 详细搜索知识库 (返回结构化结果)
     */
    Mono<List<AgentExecutionResponse.KnowledgeResult>> searchDetailed(Long agentId, String query);
    
    /**
     * 获取知识库描述文本 (用于ReAct模式)
     */
    String getKnowledgeBasesDescription(Long agentId);
    
    /**
     * 向量化文本
     */
    Mono<List<Double>> vectorizeText(String text);
    
    /**
     * 批量向量化文本
     */
    Mono<List<List<Double>>> vectorizeTexts(List<String> texts);
}
