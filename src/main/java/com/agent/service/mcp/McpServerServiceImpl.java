package com.agent.service.mcp;

import com.agent.domain.entity.AgentMcpServer;
import com.agent.domain.entity.McpServer;
import com.agent.repository.AgentMcpServerRepository;
import com.agent.repository.McpServerRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP Server服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class McpServerServiceImpl implements McpServerService {
    
    private final McpServerRepository mcpServerRepository;
    private final AgentMcpServerRepository agentMcpServerRepository;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;
    
    @Override
    public Mono<McpServer> createMcpServer(McpServer mcpServer) {
        return Mono.fromCallable(() -> {
            mcpServer.setCreatedAt(LocalDateTime.now());
            mcpServer.setUpdatedAt(LocalDateTime.now());
            return mcpServerRepository.save(mcpServer);
        });
    }
    
    @Override
    public Mono<McpServer> updateMcpServer(Long id, McpServer mcpServer) {
        return Mono.fromCallable(() -> mcpServerRepository.findById(id))
                .flatMap(optional -> {
                    if (optional.isEmpty()) {
                        return Mono.error(new IllegalArgumentException("MCP Server不存在: " + id));
                    }
                    
                    McpServer existing = optional.get();
                    existing.setName(mcpServer.getName());
                    existing.setDescription(mcpServer.getDescription());
                    existing.setType(mcpServer.getType());
                    existing.setConnectionConfig(mcpServer.getConnectionConfig());
                    existing.setAuthConfig(mcpServer.getAuthConfig());
                    existing.setEnabled(mcpServer.getEnabled());
                    existing.setUpdatedAt(LocalDateTime.now());
                    
                    return Mono.fromCallable(() -> mcpServerRepository.save(existing));
                });
    }
    
    @Override
    public Mono<Void> deleteMcpServer(Long id) {
        return Mono.fromRunnable(() -> {
            // 先删除关联关系
            List<AgentMcpServer> associations = agentMcpServerRepository.findAll()
                    .stream()
                    .filter(ams -> ams.getMcpServerId().equals(id))
                    .toList();
            agentMcpServerRepository.deleteAll(associations);
            
            // 再删除MCP Server
            mcpServerRepository.deleteById(id);
        });
    }
    
    @Override
    public Mono<McpServer> getMcpServerById(Long id) {
        return Mono.fromCallable(() -> mcpServerRepository.findById(id))
                .flatMap(optional -> optional.isPresent() ? 
                    Mono.just(optional.get()) : 
                    Mono.error(new IllegalArgumentException("MCP Server不存在: " + id)));
    }
    
    @Override
    public Flux<McpServer> getMcpServersByCreator(Long creatorId) {
        return Flux.fromIterable(mcpServerRepository.findByCreatorIdOrderByCreatedAtDesc(creatorId));
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Mono<Boolean> testMcpServerConnection(McpServer mcpServer) {
        try {
            Map<String, Object> connectionConfig = objectMapper.readValue(
                mcpServer.getConnectionConfig(), Map.class);
            
            String baseUrl = (String) connectionConfig.get("baseUrl");
            Integer timeout = (Integer) connectionConfig.getOrDefault("timeout", 30);
            
            // 构建初始化请求
            Map<String, Object> request = new HashMap<>();
            request.put("jsonrpc", "2.0");
            request.put("id", 1);
            request.put("method", "initialize");
            
            Map<String, Object> params = new HashMap<>();
            params.put("protocolVersion", "2024-11-05");
            params.put("capabilities", new HashMap<>());
            
            Map<String, Object> clientInfo = new HashMap<>();
            clientInfo.put("name", "agent-v3");
            clientInfo.put("version", "1.0.0");
            params.put("clientInfo", clientInfo);
            
            request.put("params", params);
            
            WebClient webClient = webClientBuilder.build();
            
            return webClient.post()
                    .uri(baseUrl)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .timeout(Duration.ofSeconds(timeout))
                    .map(response -> {
                        // 检查响应是否包含result字段
                        return response.containsKey("result");
                    })
                    .onErrorReturn(false);
                    
        } catch (Exception e) {
            log.error("测试MCP Server连接失败: {}", e.getMessage());
            return Mono.just(false);
        }
    }
    
    @Override
    @Transactional
    public Mono<Void> configureAgentMcpServers(Long agentId, List<AgentMcpServer> mcpServers) {
        return Mono.fromRunnable(() -> {
            // 删除现有关联
            agentMcpServerRepository.deleteByAgentId(agentId);
            
            // 添加新关联
            for (AgentMcpServer mcpServer : mcpServers) {
                mcpServer.setAgentId(agentId);
                mcpServer.setCreatedAt(LocalDateTime.now());
                mcpServer.setUpdatedAt(LocalDateTime.now());
                agentMcpServerRepository.save(mcpServer);
            }
        });
    }
    
    @Override
    public Flux<AgentMcpServer> getAgentMcpServers(Long agentId) {
        return Flux.fromIterable(agentMcpServerRepository.findByAgentIdWithMcpServer(agentId));
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Mono<String> callMcpTool(Long mcpServerId, String toolName, Map<String, Object> arguments) {
        return getMcpServerById(mcpServerId)
                .flatMap(mcpServer -> {
                    try {
                        Map<String, Object> connectionConfig = objectMapper.readValue(
                            mcpServer.getConnectionConfig(), Map.class);
                        
                        String baseUrl = (String) connectionConfig.get("baseUrl");
                        Integer timeout = (Integer) connectionConfig.getOrDefault("timeout", 30);
                        
                        // 构建工具调用请求
                        Map<String, Object> request = new HashMap<>();
                        request.put("jsonrpc", "2.0");
                        request.put("id", System.currentTimeMillis());
                        request.put("method", "tools/call");
                        
                        Map<String, Object> params = new HashMap<>();
                        params.put("name", toolName);
                        params.put("arguments", arguments);
                        request.put("params", params);
                        
                        WebClient webClient = webClientBuilder.build();
                        
                        return webClient.post()
                                .uri(baseUrl)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(request)
                                .retrieve()
                                .bodyToMono(String.class)
                                .timeout(Duration.ofSeconds(timeout));
                                
                    } catch (Exception e) {
                        log.error("调用MCP工具失败: {}", e.getMessage());
                        return Mono.error(e);
                    }
                });
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Mono<List<Map<String, Object>>> getMcpServerTools(Long mcpServerId) {
        return getMcpServerById(mcpServerId)
                .flatMap(mcpServer -> {
                    try {
                        Map<String, Object> connectionConfig = objectMapper.readValue(
                            mcpServer.getConnectionConfig(), Map.class);
                        
                        String baseUrl = (String) connectionConfig.get("baseUrl");
                        Integer timeout = (Integer) connectionConfig.getOrDefault("timeout", 30);
                        
                        // 构建工具列表请求
                        Map<String, Object> request = new HashMap<>();
                        request.put("jsonrpc", "2.0");
                        request.put("id", System.currentTimeMillis());
                        request.put("method", "tools/list");
                        request.put("params", new HashMap<>());
                        
                        WebClient webClient = webClientBuilder.build();
                        
                        return webClient.post()
                                .uri(baseUrl)
                                .contentType(MediaType.APPLICATION_JSON)
                                .bodyValue(request)
                                .retrieve()
                                .bodyToMono(Map.class)
                                .timeout(Duration.ofSeconds(timeout))
                                .map(response -> {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> result = (Map<String, Object>) response.get("result");
                                    @SuppressWarnings("unchecked")
                                    List<Map<String, Object>> tools = (List<Map<String, Object>>) result.get("tools");
                                    return tools;
                                });
                                
                    } catch (Exception e) {
                        log.error("获取MCP工具列表失败: {}", e.getMessage());
                        return Mono.error(e);
                    }
                });
    }
}
