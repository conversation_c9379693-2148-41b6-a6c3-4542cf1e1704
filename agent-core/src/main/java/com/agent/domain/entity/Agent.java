package com.agent.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent实体 - 对应Dify中的Agent配置
 */
@Entity
@Table(name = "agents")
@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Agent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    /**
     * Agent模式: REACT 或 FUNCTION_CALLING
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AgentMode mode;
    
    /**
     * 系统提示词
     */
    @Column(columnDefinition = "TEXT")
    private String systemPrompt;
    
    /**
     * 开场白
     */
    @Column(columnDefinition = "TEXT")
    private String openingStatement;
    
    /**
     * 模型配置
     */
    @Embedded
    private ModelConfig modelConfig;
    
    /**
     * Agent状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AgentStatus status = AgentStatus.DRAFT;
    
    /**
     * 创建者ID
     */
    @Column(nullable = false)
    private Long creatorId;
    
    /**
     * 关联的知识库
     */
    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<AgentKnowledge> knowledgeBases;

    /**
     * 关联的工具
     */
    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<AgentTool> tools;

    /**
     * 会话历史
     */
    @OneToMany(mappedBy = "agent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Conversation> conversations;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Agent模式枚举
     */
    public enum AgentMode {
        REACT("ReAct模式"),
        FUNCTION_CALLING("Function Calling模式");
        
        private final String description;
        
        AgentMode(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Agent状态枚举
     */
    public enum AgentStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档");
        
        private final String description;
        
        AgentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
