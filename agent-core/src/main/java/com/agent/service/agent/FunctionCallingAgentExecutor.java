package com.agent.service.agent;

import com.agent.domain.entity.Agent;
import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.dto.ChatRequest;
import com.agent.domain.dto.ChatResponse;
import com.agent.service.llm.LLMService;
import com.agent.service.tool.ToolService;
import com.agent.service.knowledge.KnowledgeService;
import com.agent.service.mcp.McpServerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Function Calling模式Agent执行器
 * 
 * Function Calling模式：
 * 1. 将用户查询和可用工具发送给LLM
 * 2. LLM决定是否需要调用函数，如果需要则返回函数调用信息
 * 3. 执行函数调用并获取结果
 * 4. 将函数结果发送回LLM获取最终回答
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FunctionCallingAgentExecutor implements AgentExecutor {

    // Reactor Context Key for Agent ID
    private static final String AGENT_ID_KEY = "agentId";
    
    private final LLMService llmService;
    private final ToolService toolService;
    private final KnowledgeService knowledgeService;
    private final McpServerService mcpServerService;
    private final ObjectMapper objectMapper;
    
    @Override
    public Agent.AgentMode getSupportedMode() {
        return Agent.AgentMode.FUNCTION_CALLING;
    }
    
    @Override
    public Mono<AgentExecutionResponse> execute(AgentExecutionRequest request) {
        log.debug("开始执行Function Calling模式Agent: agentId={}, query={}",
                request.getAgent() != null ? request.getAgent().getId() : null,
                request.getQuery());

        return validateAgentConfig(request.getAgent())
                .flatMap(valid -> {
                    if (!valid) {
                        return Mono.error(new IllegalArgumentException("Agent配置无效"));
                    }
                    return executeFunctionCalling(request);
                })
                .doOnSuccess(response -> log.debug("Function Calling执行完成: finished={}", response.getFinished()))
                .doOnError(error -> log.error("Function Calling执行失败", error));
    }
    
    @Override
    public Flux<AgentExecutionResponse> executeStream(AgentExecutionRequest request) {
        log.debug("开始流式执行Function Calling模式Agent: agentId={}, query={}",
                request.getAgent() != null ? request.getAgent().getId() : null,
                request.getQuery());

        return validateAgentConfig(request.getAgent())
                .flatMapMany(valid -> {
                    if (!valid) {
                        return Flux.error(new IllegalArgumentException("Agent配置无效"));
                    }
                    return executeFunctionCallingStream(request);
                })
                .doOnComplete(() -> log.debug("Function Calling流式执行完成"))
                .doOnError(error -> log.error("Function Calling流式执行失败", error));
    }
    
    @Override
    public Mono<Boolean> validateAgentConfig(Agent agent) {
        if (agent == null) {
            return Mono.just(false);
        }
        
        if (agent.getMode() != Agent.AgentMode.FUNCTION_CALLING) {
            return Mono.just(false);
        }
        
        if (agent.getSystemPrompt() == null || agent.getSystemPrompt().trim().isEmpty()) {
            return Mono.just(false);
        }
        
        return Mono.just(true);
    }
    
    private Mono<AgentExecutionResponse> executeFunctionCalling(AgentExecutionRequest request) {
        // 在Reactor Context中设置Agent ID

        // 构建初始聊天请求
        return buildChatRequest(request)
                .flatMap(chatRequest -> {
                    // 第一次调用LLM
                    return llmService.chat(chatRequest)
                            .flatMap(response -> {
                                if (hasFunctionCalls(response)) {
                                    // 需要执行函数调用
                                    return executeFunctionCalls(response.getToolCalls())
                                            .flatMap(functionResults -> {
                                                // 将函数结果添加到对话中，再次调用LLM
                                                return buildFollowUpChatRequest(chatRequest, response, functionResults)
                                                        .flatMap(llmService::chat)
                                                        .map(this::buildFinalResponse);
                                            });
                                } else {
                                    // 直接返回结果
                                    return Mono.just(buildFinalResponse(response));
                                }
                            });
                })
                .contextWrite(ctx -> ctx.put(AGENT_ID_KEY, request.getAgent().getId()));
    }
    
    private Flux<AgentExecutionResponse> executeFunctionCallingStream(AgentExecutionRequest request) {
        log.debug("开始流式执行Function Calling模式Agent: agentId={}, query={}",
                request.getAgent().getId(), request.getQuery());

        return buildChatRequest(request)
                .flatMapMany(chatRequest -> {
                    log.debug("开始执行流式Function Calling");
                    return executeStreamingFunctionCalling(chatRequest, request);
                })
                .contextWrite(ctx -> ctx.put(AGENT_ID_KEY, request.getAgent().getId()));
    }

    /**
     * 执行流式Function Calling逻辑
     */
    private Flux<AgentExecutionResponse> executeStreamingFunctionCalling(ChatRequest chatRequest, AgentExecutionRequest request) {
        log.debug("发送聊天请求到LLM服务");

        return llmService.chatStream(chatRequest)
                .collectList()
                .flatMapMany(responses -> {
                    log.debug("收到LLM流式响应完成，共{}个响应", responses.size());

                    // 合并所有响应
                    ChatResponse finalResponse = mergeChatResponses(responses);
                    log.debug("合并后的响应: content='{}', finishReason={}, toolCalls={}",
                            finalResponse.getContent(), finalResponse.getFinishReason(),
                            finalResponse.getToolCalls() != null ? finalResponse.getToolCalls().size() : 0);

                    // 检查是否需要执行工具调用
                    if ("tool_calls".equals(finalResponse.getFinishReason()) &&
                        finalResponse.getToolCalls() != null && !finalResponse.getToolCalls().isEmpty()) {

                        log.debug("检测到工具调用，开始执行工具");
                        return executeToolCallsAndContinue(chatRequest, finalResponse, request);
                    } else {
                        // 直接返回响应
                        log.debug("无工具调用，直接返回响应");
                        return Flux.just(AgentExecutionResponse.builder()
                                .content(finalResponse.getContent() != null ? finalResponse.getContent() : "")
                                .finished(true)
                                .isStream(true)
                                .executionTime(System.currentTimeMillis())
                                .timestamp(LocalDateTime.now())
                                .build());
                    }
                });
    }

    /**
     * 合并多个流式响应
     */
    private ChatResponse mergeChatResponses(List<ChatResponse> responses) {
        if (responses.isEmpty()) {
            return new ChatResponse();
        }

        StringBuilder contentBuilder = new StringBuilder();
        String finishReason = null;
        Map<String, Map<String, Object>> mergedToolCalls = new HashMap<>();

        for (ChatResponse response : responses) {
            if (response.getContent() != null) {
                contentBuilder.append(response.getContent());
            }
            if (response.getFinishReason() != null) {
                finishReason = response.getFinishReason();
            }
            if (response.getToolCalls() != null) {
                // 合并工具调用 - 使用index作为主键，因为流式响应中id可能不一致
                for (Map<String, Object> toolCall : response.getToolCalls()) {
                    Integer index = (Integer) toolCall.get("index");
                    String key = "tool_" + (index != null ? index : 0);

                    Map<String, Object> existingCall = mergedToolCalls.get(key);
                    if (existingCall == null) {
                        // 新的工具调用
                        existingCall = new HashMap<>(toolCall);
                        mergedToolCalls.put(key, existingCall);
                    } else {
                        // 合并现有的工具调用
                        mergeToolCall(existingCall, toolCall);
                    }
                }
            }
        }

        ChatResponse merged = new ChatResponse();
        merged.setContent(contentBuilder.toString());
        merged.setFinishReason(finishReason);
        merged.setToolCalls(mergedToolCalls.isEmpty() ? null : new ArrayList<>(mergedToolCalls.values()));

        return merged;
    }

    /**
     * 合并单个工具调用
     */
    @SuppressWarnings("unchecked")
    private void mergeToolCall(Map<String, Object> existing, Map<String, Object> newCall) {
        // 合并基本字段 - 只在新值不为空且不为空字符串时才覆盖
        if (newCall.get("id") != null && !newCall.get("id").toString().isEmpty()) {
            existing.put("id", newCall.get("id"));
        }
        if (newCall.get("type") != null) {
            existing.put("type", newCall.get("type"));
        }
        if (newCall.get("index") != null) {
            existing.put("index", newCall.get("index"));
        }

        // 合并function字段
        Map<String, Object> existingFunction = (Map<String, Object>) existing.get("function");
        Map<String, Object> newFunction = (Map<String, Object>) newCall.get("function");

        if (newFunction != null) {
            if (existingFunction == null) {
                existingFunction = new HashMap<>();
                existing.put("function", existingFunction);
            }

            // 合并function的字段 - 保留已有的name，只在没有时才设置
            if (newFunction.get("name") != null && existingFunction.get("name") == null) {
                existingFunction.put("name", newFunction.get("name"));
            }

            // 合并arguments字段
            String existingArgs = (String) existingFunction.get("arguments");
            String newArgs = (String) newFunction.get("arguments");

            if (newArgs != null) {
                if (existingArgs == null) {
                    existingFunction.put("arguments", newArgs);
                } else {
                    existingFunction.put("arguments", existingArgs + newArgs);
                }
            }
        }
    }

    /**
     * 执行工具调用并继续对话
     */
    private Flux<AgentExecutionResponse> executeToolCallsAndContinue(ChatRequest originalRequest, ChatResponse toolCallResponse, AgentExecutionRequest request) {
        return executeFunctionCalls(toolCallResponse.getToolCalls())
                .flatMapMany(toolResults -> {
                    log.debug("工具调用完成，结果数量: {}", toolResults.size());

                    // 构建包含工具调用历史的新请求
                    ChatRequest newRequest = buildChatRequestWithToolResults(originalRequest, toolCallResponse, toolResults);

                    // 发送第二次请求获取最终响应
                    return llmService.chatStream(newRequest)
                            .filter(chatResponse -> chatResponse.getContent() != null && !chatResponse.getContent().isEmpty())
                            .map(this::buildStreamResponse)
                            .concatWith(Flux.just(AgentExecutionResponse.builder()
                                    .content("")
                                    .finished(true)
                                    .isStream(true)
                                    .executionTime(System.currentTimeMillis())
                                    .timestamp(LocalDateTime.now())
                                    .build()));
                });
    }

    /**
     * 构建包含工具调用结果的聊天请求
     */
    private ChatRequest buildChatRequestWithToolResults(ChatRequest originalRequest, ChatResponse toolCallResponse, List<String> toolResults) {
        ChatRequest newRequest = new ChatRequest();
        newRequest.setModel(originalRequest.getModel());
        newRequest.setTemperature(originalRequest.getTemperature());
        newRequest.setMaxTokens(originalRequest.getMaxTokens());
        newRequest.setTopP(originalRequest.getTopP());
        newRequest.setFrequencyPenalty(originalRequest.getFrequencyPenalty());
        newRequest.setPresencePenalty(originalRequest.getPresencePenalty());
        newRequest.setTools(originalRequest.getTools());

        List<ChatRequest.Message> messages = new ArrayList<>(originalRequest.getMessages());

        // 添加助手的工具调用消息
        ChatRequest.Message assistantMessage = new ChatRequest.Message();
        assistantMessage.setRole("assistant");
        assistantMessage.setContent("");

        // 转换工具调用格式
        List<ChatRequest.ToolCall> toolCalls = new ArrayList<>();
        for (int i = 0; i < toolCallResponse.getToolCalls().size(); i++) {
            Map<String, Object> toolCall = toolCallResponse.getToolCalls().get(i);
            ChatRequest.ToolCall tc = new ChatRequest.ToolCall();
            tc.setId("call_" + i);
            tc.setType("function");

            ChatRequest.ToolCall.Function function = new ChatRequest.ToolCall.Function();
            @SuppressWarnings("unchecked")
            Map<String, Object> functionData = (Map<String, Object>) toolCall.get("function");
            function.setName((String) functionData.get("name"));
            function.setArguments((String) functionData.get("arguments"));
            tc.setFunction(function);

            toolCalls.add(tc);
        }
        assistantMessage.setToolCalls(toolCalls);
        messages.add(assistantMessage);

        // 添加工具结果消息
        for (int i = 0; i < toolResults.size(); i++) {
            ChatRequest.Message toolMessage = new ChatRequest.Message();
            toolMessage.setRole("tool");
            toolMessage.setContent(toolResults.get(i));
            toolMessage.setToolCallId("call_" + i);
            messages.add(toolMessage);
        }

        newRequest.setMessages(messages);
        return newRequest;
    }

    private Mono<ChatRequest> buildChatRequest(AgentExecutionRequest request) {
        Agent agent = request.getAgent();
        
        // 构建消息列表
        List<ChatRequest.Message> messages = new ArrayList<>();
        
        // 添加系统消息
        if (agent.getSystemPrompt() != null && !agent.getSystemPrompt().trim().isEmpty()) {
            ChatRequest.Message systemMessage = new ChatRequest.Message();
            systemMessage.setRole("system");
            systemMessage.setContent(agent.getSystemPrompt());
            messages.add(systemMessage);
        }
        
        // 添加用户消息
        ChatRequest.Message userMessage = new ChatRequest.Message();
        userMessage.setRole("user");
        userMessage.setContent(request.getQuery());
        messages.add(userMessage);
        
        // 构建ChatRequest
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel(agent.getModelConfig().getModelName());
        chatRequest.setMessages(messages);
        chatRequest.setTemperature(agent.getModelConfig().getTemperature());
        chatRequest.setMaxTokens(agent.getModelConfig().getMaxTokens());
        chatRequest.setTopP(agent.getModelConfig().getTopP());
        chatRequest.setFrequencyPenalty(agent.getModelConfig().getFrequencyPenalty());
        chatRequest.setPresencePenalty(agent.getModelConfig().getPresencePenalty());
        
        // 获取可用工具并添加到请求中
        return toolService.getAvailableTools(agent.getId())
                .map(tools -> {
                    if (!tools.isEmpty()) {
                        chatRequest.setTools(tools);
                    }
                    return chatRequest;
                });
    }
    
    private Mono<ChatRequest> buildFollowUpChatRequest(ChatRequest originalRequest, 
                                                      ChatResponse response, 
                                                      List<String> functionResults) {
        List<ChatRequest.Message> messages = new ArrayList<>(originalRequest.getMessages());
        
        // 添加助手的响应（包含函数调用）
        ChatRequest.Message assistantMessage = new ChatRequest.Message();
        assistantMessage.setRole("assistant");
        assistantMessage.setContent(response.getContent());
        if (response.getToolCalls() != null) {
            // 转换工具调用格式
            List<ChatRequest.ToolCall> toolCalls = convertToolCalls(response.getToolCalls());
            assistantMessage.setToolCalls(toolCalls);
        }
        messages.add(assistantMessage);
        
        // 添加函数结果消息
        for (int i = 0; i < functionResults.size(); i++) {
            ChatRequest.Message toolMessage = new ChatRequest.Message();
            toolMessage.setRole("tool");
            toolMessage.setContent(functionResults.get(i));
            toolMessage.setToolCallId("call_" + i); // 简化的ID生成
            messages.add(toolMessage);
        }
        
        ChatRequest followUpRequest = new ChatRequest();
        followUpRequest.setModel(originalRequest.getModel());
        followUpRequest.setMessages(messages);
        followUpRequest.setTemperature(originalRequest.getTemperature());
        followUpRequest.setMaxTokens(originalRequest.getMaxTokens());
        followUpRequest.setTopP(originalRequest.getTopP());
        followUpRequest.setFrequencyPenalty(originalRequest.getFrequencyPenalty());
        followUpRequest.setPresencePenalty(originalRequest.getPresencePenalty());
        followUpRequest.setTools(originalRequest.getTools());
        
        return Mono.just(followUpRequest);
    }
    
    private boolean hasFunctionCalls(ChatResponse response) {
        return response.getToolCalls() != null && !response.getToolCalls().isEmpty();
    }
    
    private Mono<List<String>> executeFunctionCalls(List<Map<String, Object>> toolCalls) {
        List<Mono<String>> functionCallMonos = new ArrayList<>();

        for (Map<String, Object> toolCall : toolCalls) {
            @SuppressWarnings("unchecked")
            Map<String, Object> function = (Map<String, Object>) toolCall.get("function");
            String functionName = (String) function.get("name");
            String arguments = (String) function.get("arguments");

            log.debug("执行函数调用: functionName={}, arguments={}", functionName, arguments);

            // 首先尝试MCP Server调用
            Mono<String> functionResult = tryMcpServerCall(functionName, arguments)
                    .switchIfEmpty(
                        // 如果MCP Server没有找到对应工具，则使用传统工具服务
                        toolService.executeTool(functionName, arguments)
                                .doOnNext(result -> log.debug("传统工具服务调用成功: {}", result))
                    )
                    .onErrorResume(error -> {
                        log.error("函数调用失败: functionName={}, error={}", functionName, error.getMessage());
                        return Mono.just("函数调用失败: " + functionName + " - " + error.getMessage());
                    });

            functionCallMonos.add(functionResult);
        }

        return Mono.zip(functionCallMonos, results -> {
            List<String> resultList = new ArrayList<>();
            for (Object result : results) {
                resultList.add((String) result);
            }
            return resultList;
        });
    }

    /**
     * 尝试通过MCP Server调用工具
     */
    private Mono<String> tryMcpServerCall(String functionName, String arguments) {
        // 从Reactor Context获取当前Agent的MCP Server配置
        return Mono.deferContextual(ctx -> {
            Long agentId = ctx.getOrDefault(AGENT_ID_KEY, null);
            log.debug("尝试MCP Server调用: agentId={}, functionName={}", agentId, functionName);

            if (agentId == null) {
                log.debug("Agent ID为空，跳过MCP Server调用");
                return Mono.empty();
            }

            return mcpServerService.getAgentMcpServers(agentId)
                    .filter(agentMcpServer -> agentMcpServer.getEnabled())
                    .sort((a, b) -> Integer.compare(a.getPriority(), b.getPriority()))
                    .flatMap(agentMcpServer -> {
                        log.debug("尝试MCP Server调用: serverId={}, functionName={}",
                                agentMcpServer.getMcpServerId(), functionName);

                        // 解析参数
                        Map<String, Object> argumentsMap;
                        try {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> parsedArgs = objectMapper.readValue(arguments, Map.class);
                            argumentsMap = parsedArgs;
                        } catch (Exception e) {
                            log.warn("解析函数参数失败: {}", e.getMessage());
                            return Mono.empty();
                        }

                        return mcpServerService.callMcpTool(agentMcpServer.getMcpServerId(), functionName, argumentsMap)
                                .doOnNext(result -> log.info("MCP Server调用成功: serverId={}, functionName={}, result={}",
                                        agentMcpServer.getMcpServerId(), functionName, result))
                                .onErrorResume(error -> {
                                    log.debug("MCP Server调用失败: serverId={}, functionName={}, error={}",
                                            agentMcpServer.getMcpServerId(), functionName, error.getMessage());
                                    return Mono.empty();
                                });
                    })
                    .next(); // 取第一个成功的结果
        });
    }



    private AgentExecutionResponse buildFinalResponse(ChatResponse response) {
        return AgentExecutionResponse.builder()
                .content(response.getContent())
                .finished(true)
                .tokenUsage(response.getUsage() != null ? 
                           AgentExecutionResponse.TokenUsage.builder()
                                   .promptTokens(response.getUsage().getPromptTokens())
                                   .completionTokens(response.getUsage().getCompletionTokens())
                                   .totalTokens(response.getUsage().getTotalTokens())
                                   .build() : null)
                .build();
    }
    
    private AgentExecutionResponse buildStreamResponse(ChatResponse response) {
        log.debug("构建流式响应: content='{}', finishReason={}", response.getContent(), response.getFinishReason());

        AgentExecutionResponse result = AgentExecutionResponse.builder()
                .content(response.getContent() != null ? response.getContent() : "")
                .finished(response.getFinishReason() != null)
                .isStream(true)
                .build();

        log.debug("构建的响应: {}", result);
        return result;
    }
    
    private ChatResponse mergeStreamResponses(List<ChatResponse> responses) {
        if (responses.isEmpty()) {
            return new ChatResponse();
        }
        
        ChatResponse merged = new ChatResponse();
        StringBuilder contentBuilder = new StringBuilder();
        
        for (ChatResponse response : responses) {
            if (response.getContent() != null) {
                contentBuilder.append(response.getContent());
            }
            
            // 使用最后一个响应的元数据
            if (response.getFinishReason() != null) {
                merged.setFinishReason(response.getFinishReason());
            }
            if (response.getToolCalls() != null) {
                merged.setToolCalls(response.getToolCalls());
            }
            if (response.getUsage() != null) {
                merged.setUsage(response.getUsage());
            }
        }
        
        merged.setContent(contentBuilder.toString());
        return merged;
    }
    
    private List<ChatRequest.ToolCall> convertToolCalls(List<Map<String, Object>> toolCalls) {
        List<ChatRequest.ToolCall> converted = new ArrayList<>();
        
        for (Map<String, Object> toolCall : toolCalls) {
            ChatRequest.ToolCall convertedCall = new ChatRequest.ToolCall();
            convertedCall.setId((String) toolCall.get("id"));
            convertedCall.setType((String) toolCall.get("type"));
            
            @SuppressWarnings("unchecked")
            Map<String, Object> function = (Map<String, Object>) toolCall.get("function");
            if (function != null) {
                ChatRequest.ToolCall.Function convertedFunction = new ChatRequest.ToolCall.Function();
                convertedFunction.setName((String) function.get("name"));
                convertedFunction.setArguments((String) function.get("arguments"));
                convertedCall.setFunction(convertedFunction);
            }
            
            converted.add(convertedCall);
        }
        
        return converted;
    }
}
