package com.agent.service.llm;

import com.agent.domain.dto.ChatRequest;
import com.agent.domain.dto.ChatResponse;
import com.agent.domain.dto.EmbeddingRequest;
import com.agent.domain.dto.EmbeddingResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 大语言模型服务接口
 */
public interface LLMService {
    
    /**
     * 同步聊天
     */
    Mono<ChatResponse> chat(ChatRequest request);
    
    /**
     * 流式聊天
     */
    Flux<ChatResponse> chatStream(ChatRequest request);
    
    /**
     * 生成嵌入向量
     */
    Mono<EmbeddingResponse> embedding(EmbeddingRequest request);
    
    /**
     * 批量生成嵌入向量
     */
    Mono<EmbeddingResponse> batchEmbedding(EmbeddingRequest request);
    
    /**
     * 检查模型是否可用
     */
    Mono<Boolean> isModelAvailable(String modelName);
    
    /**
     * 获取支持的模型列表
     */
    Mono<java.util.List<String>> getSupportedModels();
}
