package com.agent.context.compression.impl;

import com.agent.context.compression.ContextCompressionService;
import com.agent.context.config.ContextProperties;
import com.agent.context.model.ContextData;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * GZIP上下文压缩服务实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GzipContextCompressionService implements ContextCompressionService {

    private final ObjectMapper objectMapper;
    private final ContextProperties contextProperties;

    @Override
    public Mono<ContextData> compress(ContextData contextData) {
        if (!shouldCompress(contextData)) {
            return Mono.just(contextData);
        }

        return Mono.fromCallable(() -> {
            try {
                // 序列化内容
                String contentJson = objectMapper.writeValueAsString(contextData.getContent());
                byte[] originalBytes = contentJson.getBytes();
                long originalSize = originalBytes.length;

                // GZIP压缩
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
                    gzipOut.write(originalBytes);
                }
                byte[] compressedBytes = baos.toByteArray();
                long compressedSize = compressedBytes.length;

                // Base64编码
                String compressedContent = Base64.getEncoder().encodeToString(compressedBytes);

                // 创建压缩后的上下文数据
                ContextData compressedContextData = ContextData.builder()
                        .contextId(contextData.getContextId())
                        .type(contextData.getType())
                        .entityId(contextData.getEntityId())
                        .content(Map.of("compressed_data", compressedContent))
                        .metadata(contextData.getMetadata())
                        .version(contextData.getVersion())
                        .createdAt(contextData.getCreatedAt())
                        .updatedAt(LocalDateTime.now())
                        .expiresAt(contextData.getExpiresAt())
                        .compressed(true)
                        .compressionAlgorithm(getAlgorithmName())
                        .originalSize(originalSize)
                        .compressedSize(compressedSize)
                        .accessCount(contextData.getAccessCount())
                        .lastAccessedAt(contextData.getLastAccessedAt())
                        .createdBy(contextData.getCreatedBy())
                        .updatedBy(contextData.getUpdatedBy())
                        .tags(contextData.getTags())
                        .priority(contextData.getPriority())
                        .readOnly(contextData.getReadOnly())
                        .build();

                double ratio = getCompressionRatio(originalSize, compressedSize);
                log.debug("上下文压缩完成: contextId={}, 原始大小={}, 压缩后大小={}, 压缩比={:.2f}%",
                        contextData.getContextId(), originalSize, compressedSize, ratio * 100);

                return compressedContextData;

            } catch (IOException e) {
                throw new RuntimeException("压缩上下文数据失败: " + contextData.getContextId(), e);
            }
        });
    }

    @Override
    public Mono<ContextData> decompress(ContextData contextData) {
        if (!Boolean.TRUE.equals(contextData.getCompressed())) {
            return Mono.just(contextData);
        }

        return Mono.fromCallable(() -> {
            try {
                // 获取压缩数据
                Map<String, Object> content = contextData.getContent();
                String compressedContent = (String) content.get("compressed_data");
                
                if (compressedContent == null) {
                    throw new IllegalArgumentException("压缩数据不存在");
                }

                // Base64解码
                byte[] compressedBytes = Base64.getDecoder().decode(compressedContent);

                // GZIP解压缩
                ByteArrayInputStream bais = new ByteArrayInputStream(compressedBytes);
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                
                try (GZIPInputStream gzipIn = new GZIPInputStream(bais)) {
                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = gzipIn.read(buffer)) != -1) {
                        baos.write(buffer, 0, len);
                    }
                }

                // 反序列化内容
                String contentJson = baos.toString();
                @SuppressWarnings("unchecked")
                Map<String, Object> originalContent = objectMapper.readValue(contentJson, Map.class);

                // 创建解压缩后的上下文数据
                ContextData decompressedContextData = ContextData.builder()
                        .contextId(contextData.getContextId())
                        .type(contextData.getType())
                        .entityId(contextData.getEntityId())
                        .content(originalContent)
                        .metadata(contextData.getMetadata())
                        .version(contextData.getVersion())
                        .createdAt(contextData.getCreatedAt())
                        .updatedAt(contextData.getUpdatedAt())
                        .expiresAt(contextData.getExpiresAt())
                        .compressed(false)
                        .compressionAlgorithm(null)
                        .originalSize(contextData.getOriginalSize())
                        .compressedSize(null)
                        .accessCount(contextData.getAccessCount())
                        .lastAccessedAt(contextData.getLastAccessedAt())
                        .createdBy(contextData.getCreatedBy())
                        .updatedBy(contextData.getUpdatedBy())
                        .tags(contextData.getTags())
                        .priority(contextData.getPriority())
                        .readOnly(contextData.getReadOnly())
                        .build();

                log.debug("上下文解压缩完成: contextId={}", contextData.getContextId());

                return decompressedContextData;

            } catch (IOException e) {
                throw new RuntimeException("解压缩上下文数据失败: " + contextData.getContextId(), e);
            }
        });
    }

    @Override
    public boolean shouldCompress(ContextData contextData) {
        if (Boolean.TRUE.equals(contextData.getCompressed())) {
            return false; // 已经压缩过了
        }

        if (contextProperties.getCompression().getThreshold() <= 0) {
            return false; // 压缩功能未启用
        }

        try {
            // 计算内容大小
            String contentJson = objectMapper.writeValueAsString(contextData.getContent());
            int contentSize = contentJson.getBytes().length;
            
            return contentSize >= contextProperties.getCompression().getThreshold();
        } catch (Exception e) {
            log.warn("计算上下文大小失败: {}", contextData.getContextId(), e);
            return false;
        }
    }

    @Override
    public String getAlgorithmName() {
        return "gzip";
    }

    @Override
    public double getCompressionRatio(long originalSize, long compressedSize) {
        if (originalSize == 0) {
            return 0.0;
        }
        return (double) compressedSize / originalSize;
    }
}
