package com.agent.controller;

import com.agent.domain.entity.AgentMcpServer;
import com.agent.domain.entity.McpServer;
import com.agent.service.mcp.McpServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * MCP Server控制器
 */
@RestController
@RequestMapping("/v1/mcp-servers")
@RequiredArgsConstructor
@Slf4j
public class McpServerController {
    
    private final McpServerService mcpServerService;
    
    /**
     * 创建MCP Server
     */
    @PostMapping
    public Mono<ResponseEntity<McpServer>> createMcpServer(@RequestBody McpServer mcpServer) {
        return mcpServerService.createMcpServer(mcpServer)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
    
    /**
     * 更新MCP Server
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<McpServer>> updateMcpServer(@PathVariable Long id, @RequestBody McpServer mcpServer) {
        return mcpServerService.updateMcpServer(id, mcpServer)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
    
    /**
     * 删除MCP Server
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deleteMcpServer(@PathVariable Long id) {
        return mcpServerService.deleteMcpServer(id)
                .then(Mono.just(ResponseEntity.ok().<Void>build()))
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
    
    /**
     * 获取MCP Server详情
     */
    @GetMapping("/{id}")
    public Mono<ResponseEntity<McpServer>> getMcpServer(@PathVariable Long id) {
        return mcpServerService.getMcpServerById(id)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取用户的MCP Server列表
     */
    @GetMapping
    public Flux<McpServer> getMcpServers(@RequestParam Long creatorId) {
        return mcpServerService.getMcpServersByCreator(creatorId);
    }
    
    /**
     * 测试MCP Server连接
     */
    @PostMapping("/test")
    public Mono<ResponseEntity<Map<String, Object>>> testMcpServer(@RequestBody McpServer mcpServer) {
        return mcpServerService.testMcpServerConnection(mcpServer)
                .map(success -> {
                    Map<String, Object> result = Map.of(
                        "success", success,
                        "message", success ? "连接测试成功" : "连接测试失败"
                    );
                    return ResponseEntity.ok(result);
                })
                .onErrorReturn(ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "连接测试异常"
                )));
    }
    
    /**
     * 获取MCP Server的工具列表
     */
    @GetMapping("/{id}/tools")
    public Mono<ResponseEntity<List<Map<String, Object>>>> getMcpServerTools(@PathVariable Long id) {
        return mcpServerService.getMcpServerTools(id)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
    
    /**
     * 调用MCP Server工具
     */
    @PostMapping("/{id}/tools/{toolName}")
    public Mono<ResponseEntity<String>> callMcpTool(
            @PathVariable Long id,
            @PathVariable String toolName,
            @RequestBody Map<String, Object> arguments) {
        
        return mcpServerService.callMcpTool(id, toolName, arguments)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
    
    /**
     * 获取Agent的MCP Server配置
     */
    @GetMapping("/agent/{agentId}")
    public Flux<AgentMcpServer> getAgentMcpServers(@PathVariable Long agentId) {
        return mcpServerService.getAgentMcpServers(agentId);
    }
    
    /**
     * 配置Agent的MCP Server
     */
    @PostMapping("/agent/{agentId}")
    public Mono<ResponseEntity<Void>> configureAgentMcpServers(
            @PathVariable Long agentId,
            @RequestBody List<AgentMcpServer> mcpServers) {
        
        return mcpServerService.configureAgentMcpServers(agentId, mcpServers)
                .then(Mono.just(ResponseEntity.ok().<Void>build()))
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
}
