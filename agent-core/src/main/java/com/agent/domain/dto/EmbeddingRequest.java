package com.agent.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 嵌入向量请求DTO
 */
@Data
public class EmbeddingRequest {
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 输入文本 (可以是字符串或字符串列表)
     */
    private Object input;
    
    /**
     * 输入类型: query(查询) 或 document(文档)
     */
    private String inputType;
    
    /**
     * 用户ID (可选)
     */
    private String user;
    
    // 便捷构造方法
    public static EmbeddingRequest of(String model, String text) {
        EmbeddingRequest request = new EmbeddingRequest();
        request.setModel(model);
        request.setInput(text);
        return request;
    }
    
    public static EmbeddingRequest of(String model, List<String> texts) {
        EmbeddingRequest request = new EmbeddingRequest();
        request.setModel(model);
        request.setInput(texts);
        return request;
    }
}
