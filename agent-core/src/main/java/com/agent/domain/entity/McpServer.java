package com.agent.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * MCP Server实体
 */
@Entity
@Table(name = "mcp_servers")
@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class McpServer {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * MCP Server名称
     */
    @Column(nullable = false, length = 100)
    private String name;
    
    /**
     * 描述
     */
    @Column(length = 500)
    private String description;
    
    /**
     * MCP Server类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private McpServerType type;
    
    /**
     * 连接配置 (JSON格式存储)
     */
    @Column(name = "connection_config", columnDefinition = "TEXT")
    private String connectionConfig;
    
    /**
     * 认证配置 (JSON格式存储)
     */
    @Column(name = "auth_config", columnDefinition = "TEXT")
    private String authConfig;
    
    /**
     * 服务器状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private McpServerStatus status = McpServerStatus.INACTIVE;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 创建者ID
     */
    @Column(nullable = false)
    private Long creatorId;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 最后连接时间
     */
    @Column(name = "last_connected_at")
    private LocalDateTime lastConnectedAt;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * MCP Server类型枚举
     */
    public enum McpServerType {
        HTTP,           // HTTP/REST API
        WEBSOCKET,      // WebSocket连接
        STDIO,          // 标准输入输出
        TCP,            // TCP连接
        CUSTOM          // 自定义协议
    }
    
    /**
     * MCP Server状态枚举
     */
    public enum McpServerStatus {
        ACTIVE,         // 活跃
        INACTIVE,       // 非活跃
        CONNECTING,     // 连接中
        ERROR,          // 错误
        MAINTENANCE     // 维护中
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
