# rc-overflow 🐾

[![NPM version][npm-image]][npm-url]
[![npm download][download-image]][download-url]
[![build status][github-actions-image]][github-actions-url]
[![Codecov][codecov-image]][codecov-url]
[![bundle size][bundlephobia-image]][bundlephobia-url]
[![dumi][dumi-image]][dumi-url]

[npm-image]: http://img.shields.io/npm/v/rc-overflow.svg?style=flat-square
[npm-url]: http://npmjs.org/package/rc-overflow
[github-actions-image]: https://github.com/react-component/overflow/workflows/CI/badge.svg
[github-actions-url]: https://github.com/react-component/overflow/actions
[codecov-image]: https://img.shields.io/codecov/c/github/react-component/overflow/master.svg?style=flat-square
[codecov-url]: https://codecov.io/gh/react-component/overflow/branch/master
[david-url]: https://david-dm.org/react-component/overflow
[david-image]: https://david-dm.org/react-component/overflow/status.svg?style=flat-square
[david-dev-url]: https://david-dm.org/react-component/overflow?type=dev
[david-dev-image]: https://david-dm.org/react-component/overflow/dev-status.svg?style=flat-square
[download-image]: https://img.shields.io/npm/dm/rc-overflow.svg?style=flat-square
[download-url]: https://npmjs.org/package/rc-overflow
[bundlephobia-url]: https://bundlephobia.com/result?p=rc-overflow
[bundlephobia-image]: https://badgen.net/bundlephobia/minzip/rc-overflow
[dumi-url]: https://github.com/umijs/dumi
[dumi-image]: https://img.shields.io/badge/docs%20by-dumi-blue?style=flat-square

Auto collapse box when overflow

## Live Demo

https://overflow-react-component.vercel.app/

## Install

[![rc-overflow](https://nodei.co/npm/rc-overflow.png)](https://npmjs.org/package/rc-overflow)

## Usage

```ts
// TODO
```

## API

| Property | Type | Default | Description |
| -------- | ---- | ------- | ----------- |

## Development

```
npm install
npm start
```

## License

rc-overflow is released under the MIT license.
