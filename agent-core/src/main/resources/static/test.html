<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端流式数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .output {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>前端流式数据测试</h1>

    <!-- 测试1: 简单流式数据 -->
    <div class="test-section">
        <h3>测试1: 简单流式数据</h3>
        <button onclick="testSimpleStream()">开始测试</button>
        <div id="simple-output" class="output"></div>
    </div>

    <!-- 测试2: 阿里云LLM流式数据 -->
    <div class="test-section">
        <h3>测试2: 阿里云LLM流式数据</h3>
        <input type="text" id="message-input" placeholder="输入消息" value="你好">
        <button onclick="testAliyunStream()">开始测试</button>
        <div id="aliyun-output" class="output"></div>
    </div>

    <!-- 测试3: 原始Agent API -->
    <div class="test-section">
        <h3>测试3: 原始Agent API</h3>
        <button onclick="testAgentStream()">开始测试</button>
        <div id="agent-output" class="output"></div>
    </div>

    <!-- 测试4: Fetch API vs EventSource -->
    <div class="test-section">
        <h3>测试4: Fetch API vs EventSource</h3>
        <button onclick="testFetchAPI()">Fetch API测试</button>
        <button onclick="testEventSource()">EventSource测试</button>
        <div id="comparison-output" class="output"></div>
    </div>

    <script>
        // 测试1: 简单流式数据
        async function testSimpleStream() {
            const output = document.getElementById('simple-output');
            output.textContent = '开始测试简单流式数据...\n';

            try {
                const response = await fetch('/test/stream/simple', {
                    headers: {
                        'Accept': 'text/event-stream'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    output.textContent += `收到数据块: ${chunk}\n`;
                    
                    const lines = chunk.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            if (data === '[DONE]') {
                                output.textContent += '✅ 测试完成\n';
                                return;
                            }
                            // 处理纯Markdown格式数据
                            if (data && data.trim() !== '') {
                                output.textContent += `Markdown数据: ${data}\n`;
                            }
                        }
                    }
                }
            } catch (error) {
                output.textContent += `❌ 错误: ${error.message}\n`;
            }
        }

        // 测试2: 阿里云LLM流式数据
        async function testAliyunStream() {
            const output = document.getElementById('aliyun-output');
            const message = document.getElementById('message-input').value;
            output.textContent = `开始测试阿里云LLM流式数据: ${message}\n`;

            try {
                const response = await fetch('/test/stream/aliyun', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({ message: message })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            if (data === '[DONE]') {
                                output.textContent += '\n✅ 测试完成\n';
                                return;
                            }
                            // 处理纯Markdown格式数据
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                output.textContent = `开始测试阿里云LLM流式数据: ${message}\n完整内容: ${fullContent}\n`;
                            }
                        }
                    }
                }
            } catch (error) {
                output.textContent += `❌ 错误: ${error.message}\n`;
            }
        }

        // 测试3: 原始Agent API
        async function testAgentStream() {
            const output = document.getElementById('agent-output');
            output.textContent = '开始测试原始Agent API...\n';

            try {
                const response = await fetch('/v1/agents/4/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好',
                        userId: 1,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            if (data === '[DONE]') {
                                output.textContent += '\n✅ 测试完成\n';
                                return;
                            }
                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.content) {
                                    fullContent += parsed.content;
                                    output.textContent = `开始测试原始Agent API...\n完整内容: ${fullContent}\n`;
                                }
                            } catch (e) {
                                output.textContent += `❌ 解析失败: ${data}\n`;
                            }
                        }
                    }
                }
            } catch (error) {
                output.textContent += `❌ 错误: ${error.message}\n`;
            }
        }

        // 测试4: Fetch API
        async function testFetchAPI() {
            const output = document.getElementById('comparison-output');
            output.textContent = 'Fetch API测试开始...\n';

            try {
                const response = await fetch('/test/stream/simple', {
                    headers: { 'Accept': 'text/event-stream' }
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    output.textContent += `Fetch收到: ${chunk}`;
                }
            } catch (error) {
                output.textContent += `Fetch错误: ${error.message}\n`;
            }
        }

        // 测试4: EventSource
        function testEventSource() {
            const output = document.getElementById('comparison-output');
            output.textContent = 'EventSource测试开始...\n';

            const eventSource = new EventSource('/test/stream/simple');
            
            eventSource.onmessage = function(event) {
                output.textContent += `EventSource收到: ${event.data}\n`;
                if (event.data === '[DONE]') {
                    eventSource.close();
                    output.textContent += '✅ EventSource测试完成\n';
                }
            };

            eventSource.onerror = function(error) {
                output.textContent += `EventSource错误: ${error}\n`;
                eventSource.close();
            };
        }
    </script>
</body>
</html>
