package com.agent.service.agent;

import com.agent.domain.entity.Agent;
import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Agent执行器接口
 */
public interface AgentExecutor {
    
    /**
     * 支持的Agent模式
     */
    Agent.AgentMode getSupportedMode();
    
    /**
     * 同步执行Agent
     */
    Mono<AgentExecutionResponse> execute(AgentExecutionRequest request);
    
    /**
     * 流式执行Agent
     */
    Flux<AgentExecutionResponse> executeStream(AgentExecutionRequest request);
    
    /**
     * 验证Agent配置是否有效
     */
    Mono<Boolean> validateAgentConfig(Agent agent);
}
