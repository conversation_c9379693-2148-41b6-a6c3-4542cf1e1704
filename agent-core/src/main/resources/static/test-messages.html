<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        .message.user {
            flex-direction: row-reverse;
        }
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }
        .message.user .message-avatar {
            background: #52c41a;
            color: white;
        }
        .message.assistant .message-avatar {
            background: #1890ff;
            color: white;
        }
        .message-content {
            max-width: 70%;
            padding: 14px 18px;
            border-radius: 12px;
            line-height: 1.6;
            word-wrap: break-word;
            font-size: 15px;
        }
        .message.user .message-content {
            background: #1890ff;
            color: white;
        }
        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }
        button {
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .api-response {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>消息显示测试</h1>
    
    <div class="test-section">
        <h3>测试API响应格式</h3>
        <button onclick="testAPI()">获取消息数据</button>
        <div id="apiResponse" class="api-response">点击按钮获取API数据</div>
    </div>
    
    <div class="test-section">
        <h3>测试消息显示</h3>
        <button onclick="testMessageDisplay()">测试消息显示</button>
        <div id="messageContainer"></div>
    </div>

    <script>
        async function testAPI() {
            try {
                const response = await fetch('/v1/conversations/5fbaeb1b-691f-45a3-9071-12c66d8512b5/messages');
                const messages = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(messages, null, 2);
            } catch (error) {
                document.getElementById('apiResponse').textContent = '错误: ' + error.message;
            }
        }

        async function testMessageDisplay() {
            try {
                const response = await fetch('/v1/conversations/5fbaeb1b-691f-45a3-9071-12c66d8512b5/messages');
                const messages = await response.json();
                
                const container = document.getElementById('messageContainer');
                container.innerHTML = messages.map(msg => {
                    const role = msg.role.toLowerCase(); // 转换为小写
                    return `
                        <div class="message ${role}">
                            <div class="message-avatar">${role === 'user' ? 'U' : 'AI'}</div>
                            <div class="message-content">${msg.content}</div>
                        </div>
                    `;
                }).join('');
                
            } catch (error) {
                document.getElementById('messageContainer').innerHTML = '错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
