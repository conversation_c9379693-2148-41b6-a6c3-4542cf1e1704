<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown聊天测试</title>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }
        .status {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 13px;
            border-left: 4px solid #2196f3;
        }
        .chat-area {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            max-width: 80%;
        }
        .message.user {
            background: #e3f2fd;
            margin-left: auto;
            text-align: right;
        }
        .message.assistant {
            background: white;
            border: 1px solid #e0e0e0;
            margin-right: auto;
        }
        .message.streaming {
            border-left: 4px solid #2196f3;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #2196f3;
        }
        button {
            padding: 12px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .examples {
            background: #fff3e0;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .examples h4 {
            margin: 0 0 10px 0;
            color: #f57c00;
        }
        .examples code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        /* Markdown样式 */
        .message h1, .message h2, .message h3 {
            margin: 12px 0 8px 0;
            color: #333;
        }
        .message h1 {
            font-size: 1.4em;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .message h2 {
            font-size: 1.2em;
            color: #2c3e50;
        }
        .message h3 {
            font-size: 1.1em;
            color: #34495e;
        }
        .message ul, .message ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .message li {
            margin: 4px 0;
        }
        .message strong {
            color: #2c3e50;
            font-weight: 600;
        }
        .message p {
            margin: 8px 0;
            line-height: 1.5;
        }
        .message code {
            background: #f8f8f8;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }
        .message pre {
            background: #f8f8f8;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Markdown聊天测试</h1>
            <p><strong>版本：</strong>2025.08.04 - 纯Markdown解析，无JSON处理</p>
        </div>
        
        <div class="status" id="status">
            📊 状态：初始化中... | marked库：<span id="markedStatus">检查中...</span>
        </div>
        
        <div class="examples">
            <h4>💡 测试示例</h4>
            <p>试试这些消息：</p>
            <ul>
                <li><code>请用markdown格式介绍**人工智能**</code></li>
                <li><code>用markdown写一个# 标题和- 列表</code></li>
                <li><code>介绍Python，包含### 子标题</code></li>
            </ul>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div class="message assistant">
                <strong>🤖 AI助手：</strong>你好！我现在支持完整的Markdown格式渲染。发送消息试试吧！
            </div>
        </div>
        
        <div class="input-group">
            <input type="text" id="messageInput" placeholder="输入你的消息..." />
            <button onclick="sendMessage()" id="sendBtn">发送</button>
            <button onclick="clearChat()">清空</button>
        </div>
    </div>

    <script>
        const AGENT_ID = 7;
        let isLoading = false;

        // 页面加载完成后检查marked库
        window.addEventListener('load', function() {
            const markedStatus = document.getElementById('markedStatus');
            const status = document.getElementById('status');
            
            if (typeof marked !== 'undefined' && marked.parse) {
                markedStatus.innerHTML = '<span style="color: green;">✅ 已加载</span>';
                status.innerHTML = '📊 状态：就绪 | marked库：<span style="color: green;">✅ 已加载</span>';
                
                // 测试marked库
                try {
                    const testResult = marked.parse('**测试**');
                    console.log('✅ marked库测试成功:', testResult);
                } catch (e) {
                    console.error('❌ marked库测试失败:', e);
                    markedStatus.innerHTML = '<span style="color: red;">❌ 测试失败</span>';
                }
            } else {
                markedStatus.innerHTML = '<span style="color: red;">❌ 未加载</span>';
                status.innerHTML = '📊 状态：marked库加载失败 | marked库：<span style="color: red;">❌ 未加载</span>';
            }
        });

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            isLoading = true;
            updateUI(true);
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            
            // 添加AI消息占位符
            const assistantDiv = addMessage('assistant', '', true);
            
            try {
                await processStreamMessage(message, assistantDiv);
            } catch (error) {
                console.error('❌ 发送失败:', error);
                assistantDiv.innerHTML = '<strong>🤖 AI助手：</strong>❌ 发送失败: ' + error.message;
                assistantDiv.classList.remove('streaming');
                updateStatus('❌ 发送失败: ' + error.message);
            } finally {
                isLoading = false;
                updateUI(false);
            }
        }

        // 处理流式消息 - 纯Markdown，无JSON解析
        async function processStreamMessage(message, assistantDiv) {
            updateStatus('🚀 发送请求到Agent ' + AGENT_ID + '...');
            
            const response = await fetch(`/v1/agents/${AGENT_ID}/execute/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify({
                    query: message,
                    userId: 1,
                    stream: true
                })
            });

            updateStatus(`📡 收到响应: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            updateStatus('📥 开始接收流数据...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    updateStatus('✅ 流数据接收完成');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                console.log('📦 收到数据块:', chunk);
                
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        const data = line.slice(5); // 移除 'data:' 前缀
                        console.log('📄 提取数据:', data);
                        
                        if (data === '[DONE]') {
                            console.log('🏁 检测到结束标记');
                            assistantDiv.classList.remove('streaming');
                            updateStatus('🎉 对话完成！内容长度: ' + fullContent.length);
                            return;
                        }
                        
                        // 纯Markdown数据处理 - 不使用JSON.parse
                        if (data && data.trim() !== '') {
                            fullContent += data;
                            updateAssistantMessage(assistantDiv, fullContent);
                            updateStatus(`📝 接收中... 当前长度: ${fullContent.length}`);
                        }
                    }
                }
            }
            
            // 流结束处理
            assistantDiv.classList.remove('streaming');
            updateStatus('✅ 对话完成！最终长度: ' + fullContent.length);
        }

        // 更新AI消息内容并渲染Markdown
        function updateAssistantMessage(div, content) {
            if (typeof marked !== 'undefined' && marked.parse) {
                try {
                    const rendered = marked.parse(content);
                    div.innerHTML = '<strong>🤖 AI助手：</strong>' + rendered;
                    console.log('✅ Markdown渲染成功，长度:', rendered.length);
                } catch (error) {
                    console.error('❌ Markdown渲染失败:', error);
                    div.innerHTML = '<strong>🤖 AI助手：</strong>' + escapeHtml(content);
                }
            } else {
                console.warn('⚠️ marked库不可用，使用纯文本');
                div.innerHTML = '<strong>🤖 AI助手：</strong>' + escapeHtml(content);
            }
            
            // 滚动到底部
            const chatArea = document.getElementById('chatArea');
            chatArea.scrollTop = chatArea.scrollHeight;
        }

        // 添加消息到聊天区域
        function addMessage(role, content, streaming = false) {
            const chatArea = document.getElementById('chatArea');
            const div = document.createElement('div');
            div.className = `message ${role}${streaming ? ' streaming' : ''}`;
            
            if (role === 'user') {
                div.innerHTML = `<strong>👤 用户：</strong>${escapeHtml(content)}`;
            } else {
                div.innerHTML = `<strong>🤖 AI助手：</strong>${escapeHtml(content)}`;
            }
            
            chatArea.appendChild(div);
            chatArea.scrollTop = chatArea.scrollHeight;
            
            return div;
        }

        // 更新状态显示
        function updateStatus(text) {
            document.getElementById('status').innerHTML = text;
            console.log('📊', text);
        }

        // 更新UI状态
        function updateUI(loading) {
            const sendBtn = document.getElementById('sendBtn');
            const input = document.getElementById('messageInput');
            
            sendBtn.disabled = loading;
            sendBtn.textContent = loading ? '发送中...' : '发送';
            
            if (!loading) {
                input.focus();
            }
        }

        // 清空聊天
        function clearChat() {
            const chatArea = document.getElementById('chatArea');
            chatArea.innerHTML = '<div class="message assistant"><strong>🤖 AI助手：</strong>聊天已清空，可以开始新的对话了！</div>';
            updateStatus('📊 状态：聊天已清空');
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isLoading) {
                sendMessage();
            }
        });

        // 页面加载完成后聚焦输入框
        window.addEventListener('load', function() {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
