<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent V3 - 智能AI助手平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .welcome-container {
            background: white;
            border-radius: 16px;
            padding: 80px 60px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 1000px;
            width: 90%;
        }

        @media (min-width: 1400px) {
            .welcome-container {
                max-width: 1200px;
                padding: 100px 80px;
            }
        }

        .logo {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            color: #1890ff;
            margin-bottom: 16px;
        }

        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-bottom: 50px;
        }

        @media (max-width: 1024px) {
            .features {
                grid-template-columns: repeat(2, 1fr);
                gap: 24px;
            }
        }

        .feature {
            padding: 30px 24px;
            background: #f8f9fa;
            border-radius: 12px;
            transition: transform 0.3s;
        }

        .feature:hover {
            transform: translateY(-4px);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }

        .feature-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
            min-width: 160px;
            justify-content: center;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(24, 144, 255, 0.3);
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: #1890ff;
            border: 2px solid #1890ff;
        }

        .btn-outline:hover {
            background: #1890ff;
            color: white;
            transform: translateY(-2px);
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            padding: 20px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #1890ff;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #999;
        }

        @media (max-width: 768px) {
            .welcome-container {
                padding: 40px 20px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 16px;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">🤖</div>
        <h1 class="title">Agent V3</h1>
        <p class="subtitle">
            新一代智能AI助手平台<br>
            支持多种执行模式，打造专属的AI助手
        </p>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">智能对话</div>
                <div class="feature-desc">基于大语言模型的自然对话体验</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">函数调用</div>
                <div class="feature-desc">支持Function Calling扩展能力</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📚</div>
                <div class="feature-title">知识问答</div>
                <div class="feature-desc">RAG技术实现专业知识问答</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">个性定制</div>
                <div class="feature-desc">灵活配置Agent角色和行为</div>
            </div>
        </div>

        <div class="stats" id="stats">
            <div class="stat">
                <div class="stat-number" id="agentCount">-</div>
                <div class="stat-label">可用Agent</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="conversationCount">-</div>
                <div class="stat-label">历史对话</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="messageCount">-</div>
                <div class="stat-label">消息总数</div>
            </div>
        </div>

        <div class="actions">
            <a href="agent-management.html" class="btn btn-primary">
                🛠️ Agent管理
            </a>
            <a href="index.html" class="btn btn-outline">
                💬 开始对话
            </a>
        </div>

        <div class="footer">
            <p>Agent V3 - 让AI更智能，让对话更自然</p>
        </div>
    </div>

    <script>
        // 页面加载时获取统计数据
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });

        // 加载统计数据
        async function loadStats() {
            try {
                // 加载Agent数量
                const agentsResponse = await fetch('/v1/agents');
                if (agentsResponse.ok) {
                    const agents = await agentsResponse.json();
                    document.getElementById('agentCount').textContent = agents.length;
                }

                // 加载对话数量（使用用户ID 1作为示例）
                const conversationsResponse = await fetch('/v1/conversations?userId=1');
                if (conversationsResponse.ok) {
                    const conversations = await conversationsResponse.json();
                    document.getElementById('conversationCount').textContent = conversations.length;
                    
                    // 计算消息总数
                    let totalMessages = 0;
                    for (const conv of conversations) {
                        try {
                            const messagesResponse = await fetch(`/v1/conversations/${conv.uuid}/messages`);
                            if (messagesResponse.ok) {
                                const messages = await messagesResponse.json();
                                totalMessages += messages.length;
                            }
                        } catch (e) {
                            // 忽略单个对话的错误
                        }
                    }
                    document.getElementById('messageCount').textContent = totalMessages;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                // 显示默认值
                document.getElementById('agentCount').textContent = '0';
                document.getElementById('conversationCount').textContent = '0';
                document.getElementById('messageCount').textContent = '0';
            }
        }
    </script>
</body>
</html>
