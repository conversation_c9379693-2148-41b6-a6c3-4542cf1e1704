package com.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.dashscope")
@Data
public class AliyunConfig {
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 基础URL
     */
    private String baseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    
    /**
     * 超时时间(毫秒)
     */
    private Long timeout = 60000L;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries = 3;
    
    /**
     * 模型配置
     */
    private ModelConfig model = new ModelConfig();
    
    @Data
    public static class ModelConfig {
        /**
         * 默认聊天模型
         */
        private String chat = "qwen-turbo";
        
        /**
         * 默认嵌入模型
         */
        private String embedding = "text-embedding-v1";
    }
}
