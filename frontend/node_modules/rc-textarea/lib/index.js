"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "ResizableTextArea", {
  enumerable: true,
  get: function get() {
    return _ResizableTextArea.default;
  }
});
exports.default = void 0;
var _TextArea = _interopRequireDefault(require("./TextArea"));
var _ResizableTextArea = _interopRequireDefault(require("./ResizableTextArea"));
var _default = exports.default = _TextArea.default;