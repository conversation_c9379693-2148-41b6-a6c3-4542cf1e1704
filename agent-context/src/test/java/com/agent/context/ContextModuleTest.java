package com.agent.context;

import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import com.agent.context.service.ContextService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 上下文模块测试
 */
@SpringBootTest(classes = {com.agent.context.config.ContextAutoConfiguration.class})
@TestPropertySource(properties = {
        "spring.redis.host=localhost",
        "spring.redis.port=6379",
        "agent.context.session-context.ttl=PT30M"
})
class ContextModuleTest {

    @Test
    void contextDataModelTest() {
        // 测试上下文数据模型
        ContextData contextData = ContextData.builder()
                .contextId("test-context-1")
                .type(ContextType.SESSION)
                .entityId("session-123")
                .content(Map.of("message", "Hello World", "timestamp", System.currentTimeMillis()))
                .metadata(Map.of("source", "test", "version", "1.0"))
                .version(1L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .compressed(false)
                .accessCount(0L)
                .priority(1)
                .readOnly(false)
                .build();

        assertNotNull(contextData);
        assertEquals("test-context-1", contextData.getContextId());
        assertEquals(ContextType.SESSION, contextData.getType());
        assertEquals("session-123", contextData.getEntityId());
        assertFalse(contextData.isExpired());
        
        // 测试访问计数
        contextData.incrementAccessCount();
        assertEquals(1L, contextData.getAccessCount());
        assertNotNull(contextData.getLastAccessedAt());
    }

    @Test
    void contextTypeTest() {
        // 测试上下文类型枚举
        assertEquals("session", ContextType.SESSION.getCode());
        assertEquals("会话上下文", ContextType.SESSION.getDescription());
        
        assertEquals(ContextType.AGENT, ContextType.fromCode("agent"));
        
        assertThrows(IllegalArgumentException.class, () -> {
            ContextType.fromCode("unknown");
        });
    }
}
