package com.agent;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.entity.Agent;
import com.agent.domain.entity.ModelConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "llm.mock.enabled=true"
})
@Transactional
class AgentApplicationIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testCompleteAgentWorkflow() throws Exception {
        // 1. 创建Agent
        Agent agent = createTestAgent();
        
        String createResponse = mockMvc.perform(post("/v1/agents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(agent)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("集成测试Agent"))
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Agent createdAgent = objectMapper.readValue(createResponse, Agent.class);
        Long agentId = createdAgent.getId();
        
        // 2. 获取Agent详情
        mockMvc.perform(get("/v1/agents/" + agentId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(agentId))
                .andExpect(jsonPath("$.name").value("集成测试Agent"));
        
        // 3. 验证Agent配置
        mockMvc.perform(post("/v1/agents/" + agentId + "/validate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(true));
        
        // 4. 执行Agent
        AgentExecutionRequest executionRequest = AgentExecutionRequest.builder()
                .query("你好，请介绍一下自己")
                .userId(1L)
                .build();
        
        mockMvc.perform(post("/v1/agents/" + agentId + "/execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(executionRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists())
                .andExpect(jsonPath("$.finished").value(true));
        
        // 5. 发布Agent
        mockMvc.perform(post("/v1/agents/" + agentId + "/publish"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("PUBLISHED"));
        
        // 6. 更新Agent
        agent.setName("更新后的Agent");
        agent.setId(agentId);
        
        mockMvc.perform(put("/v1/agents/" + agentId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(agent)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("更新后的Agent"));
    }
    
    @Test
    void testAgentExecutionWithDifferentModes() throws Exception {
        // 测试ReAct模式
        Agent reactAgent = createTestAgent();
        reactAgent.setMode(Agent.AgentMode.REACT);
        reactAgent.setName("ReAct测试Agent");
        
        String reactResponse = mockMvc.perform(post("/v1/agents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(reactAgent)))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Agent createdReactAgent = objectMapper.readValue(reactResponse, Agent.class);
        
        // 执行ReAct Agent
        AgentExecutionRequest request = AgentExecutionRequest.builder()
                .query("帮我搜索一下天气信息")
                .userId(1L)
                .build();
        
        mockMvc.perform(post("/v1/agents/" + createdReactAgent.getId() + "/execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
        
        // 测试Function Calling模式
        Agent fcAgent = createTestAgent();
        fcAgent.setMode(Agent.AgentMode.FUNCTION_CALLING);
        fcAgent.setName("Function Calling测试Agent");
        
        String fcResponse = mockMvc.perform(post("/v1/agents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(fcAgent)))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Agent createdFcAgent = objectMapper.readValue(fcResponse, Agent.class);
        
        // 执行Function Calling Agent
        mockMvc.perform(post("/v1/agents/" + createdFcAgent.getId() + "/execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").exists());
    }
    
    private Agent createTestAgent() {
        Agent agent = new Agent();
        agent.setName("集成测试Agent");
        agent.setDescription("这是一个集成测试Agent");
        agent.setMode(Agent.AgentMode.FUNCTION_CALLING);
        agent.setSystemPrompt("你是一个智能助手，可以帮助用户解答问题和执行任务。");
        agent.setOpeningStatement("你好！我是你的AI助手，有什么可以帮助你的吗？");
        agent.setCreatorId(1L);
        
        ModelConfig modelConfig = new ModelConfig();
        modelConfig.setModelName("qwen-turbo");
        modelConfig.setTemperature(0.7);
        modelConfig.setMaxTokens(2000);
        modelConfig.setTopP(0.9);
        modelConfig.setFrequencyPenalty(0.0);
        modelConfig.setPresencePenalty(0.0);
        modelConfig.setStreamEnabled(true);
        
        agent.setModelConfig(modelConfig);
        
        return agent;
    }
}
