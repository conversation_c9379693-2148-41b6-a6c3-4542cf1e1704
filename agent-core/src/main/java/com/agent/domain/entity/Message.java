package com.agent.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * 消息实体
 */
@Entity
@Table(name = "messages")
@Data
@EqualsAndHashCode(callSuper = false)
public class Message {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 消息UUID
     */
    @Column(nullable = false, unique = true, length = 36)
    private String uuid;
    
    /**
     * 关联的会话
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    @JsonIgnore
    private Conversation conversation;
    
    /**
     * 消息角色
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;
    
    /**
     * 消息内容
     */
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;
    
    /**
     * 消息类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageType type = MessageType.TEXT;
    
    /**
     * 父消息ID (用于构建消息树)
     */
    @Column(name = "parent_message_id")
    private Long parentMessageId;
    
    /**
     * 消息元数据 (JSON格式存储)
     */
    @Column(columnDefinition = "TEXT")
    private String metadata;
    
    /**
     * 工具调用信息 (JSON格式存储)
     */
    @Column(name = "tool_calls", columnDefinition = "TEXT")
    private String toolCalls;
    
    /**
     * 工具调用结果 (JSON格式存储)
     */
    @Column(name = "tool_results", columnDefinition = "TEXT")
    private String toolResults;
    
    /**
     * 消息状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageStatus status = MessageStatus.COMPLETED;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * Token使用统计
     */
    @Embedded
    private TokenUsage tokenUsage;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 消息角色枚举
     */
    public enum MessageRole {
        USER("用户"),
        ASSISTANT("助手"),
        SYSTEM("系统"),
        TOOL("工具");
        
        private final String description;
        
        MessageRole(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        TEXT("文本"),
        IMAGE("图片"),
        FILE("文件"),
        TOOL_CALL("工具调用"),
        TOOL_RESULT("工具结果");
        
        private final String description;
        
        MessageType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        PENDING("处理中"),
        COMPLETED("已完成"),
        ERROR("错误"),
        CANCELLED("已取消");
        
        private final String description;
        
        MessageStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * Token使用统计
     */
    @Embeddable
    @Data
    public static class TokenUsage {
        
        @Column(name = "prompt_tokens")
        private Integer promptTokens = 0;
        
        @Column(name = "completion_tokens")
        private Integer completionTokens = 0;
        
        @Column(name = "total_tokens")
        private Integer totalTokens = 0;
    }
}
