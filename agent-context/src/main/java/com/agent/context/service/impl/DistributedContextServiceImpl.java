package com.agent.context.service.impl;

import com.agent.context.cache.ContextCacheManager;
import com.agent.context.compression.ContextCompressionService;
import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import com.agent.context.service.ContextService;
import com.agent.context.storage.ContextStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 分布式上下文服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedContextServiceImpl implements ContextService {

    private final ContextCacheManager cacheManager;
    private final ContextStorageService storageService;
    private final ContextCompressionService compressionService;

    @Override
    public Mono<ContextData> getContext(String contextId, ContextType type) {
        log.debug("获取上下文: contextId={}, type={}", contextId, type);
        
        // 先从缓存获取
        return cacheManager.get(contextId, type)
                .switchIfEmpty(
                    // 缓存未命中，从存储获取
                    storageService.findById(contextId, type)
                            .flatMap(contextData -> {
                                // 解压缩（如果需要）
                                return compressionService.decompress(contextData);
                            })
                            .flatMap(contextData -> {
                                // 更新到缓存
                                Duration ttl = getTtlByType(type);
                                return cacheManager.put(contextData, ttl)
                                        .thenReturn(contextData);
                            })
                )
                .doOnNext(contextData -> {
                    if (contextData != null) {
                        contextData.incrementAccessCount();
                    }
                });
    }

    @Override
    public Mono<ContextData> saveContext(ContextData contextData) {
        log.debug("保存上下文: contextId={}, type={}", contextData.getContextId(), contextData.getType());
        
        // 设置时间戳
        LocalDateTime now = LocalDateTime.now();
        if (contextData.getCreatedAt() == null) {
            contextData.setCreatedAt(now);
        }
        contextData.setUpdatedAt(now);
        
        // 压缩（如果需要）
        return compressionService.compress(contextData)
                .flatMap(compressedData -> {
                    // 保存到存储
                    return storageService.save(compressedData);
                })
                .flatMap(savedData -> {
                    // 更新到缓存
                    Duration ttl = getTtlByType(savedData.getType());
                    return cacheManager.put(savedData, ttl)
                            .thenReturn(savedData);
                });
    }

    @Override
    public Mono<ContextData> updateContext(String contextId, ContextType type, Map<String, Object> content) {
        log.debug("更新上下文: contextId={}, type={}", contextId, type);
        
        return getContext(contextId, type)
                .switchIfEmpty(Mono.error(new RuntimeException("上下文不存在: " + contextId)))
                .map(contextData -> {
                    contextData.setContent(content);
                    contextData.setUpdatedAt(LocalDateTime.now());
                    if (contextData.getVersion() != null) {
                        contextData.setVersion(contextData.getVersion() + 1);
                    }
                    return contextData;
                })
                .flatMap(this::saveContext);
    }

    @Override
    public Mono<Void> deleteContext(String contextId, ContextType type) {
        log.debug("删除上下文: contextId={}, type={}", contextId, type);
        
        return Mono.when(
                cacheManager.delete(contextId, type),
                storageService.deleteById(contextId, type)
        );
    }

    @Override
    public Flux<ContextData> getContexts(List<String> contextIds, ContextType type) {
        log.debug("批量获取上下文: contextIds={}, type={}", contextIds, type);
        
        return Flux.fromIterable(contextIds)
                .flatMap(contextId -> getContext(contextId, type));
    }

    @Override
    public Flux<ContextData> getContextsByEntity(String entityId, ContextType type) {
        log.debug("根据实体ID获取上下文: entityId={}, type={}", entityId, type);
        
        return storageService.findByEntityId(entityId, type)
                .flatMap(compressionService::decompress);
    }

    @Override
    public Mono<Void> setContextTtl(String contextId, ContextType type, Duration ttl) {
        log.debug("设置上下文过期时间: contextId={}, type={}, ttl={}", contextId, type, ttl);
        
        return cacheManager.expire(contextId, type, ttl);
    }

    @Override
    public Mono<Boolean> existsContext(String contextId, ContextType type) {
        return cacheManager.exists(contextId, type)
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.just(true);
                    }
                    return storageService.existsById(contextId, type);
                });
    }

    @Override
    public Mono<Long> cleanupExpiredContexts(ContextType type) {
        log.info("清理过期上下文: type={}", type);
        
        return Mono.when(
                cacheManager.cleanupExpired(type),
                storageService.cleanupExpired(type)
        ).then(Mono.just(0L));
    }

    @Override
    public Mono<Map<String, Object>> getContextStats(ContextType type) {
        return storageService.getStorageStats(type);
    }

    @Override
    public Mono<ContextData> mergeContexts(List<String> contextIds, ContextType type) {
        // 简单实现：获取所有上下文并合并内容
        return getContexts(contextIds, type)
                .collectList()
                .map(contextDataList -> {
                    if (contextDataList.isEmpty()) {
                        return null;
                    }
                    
                    ContextData merged = contextDataList.get(0);
                    for (int i = 1; i < contextDataList.size(); i++) {
                        ContextData current = contextDataList.get(i);
                        merged.getContent().putAll(current.getContent());
                    }
                    
                    return merged;
                });
    }

    @Override
    public Mono<ContextData> copyContext(String sourceContextId, String targetContextId, ContextType type) {
        return getContext(sourceContextId, type)
                .map(sourceContext -> {
                    ContextData copy = ContextData.builder()
                            .contextId(targetContextId)
                            .type(sourceContext.getType())
                            .entityId(sourceContext.getEntityId())
                            .content(Map.copyOf(sourceContext.getContent()))
                            .metadata(sourceContext.getMetadata() != null ? Map.copyOf(sourceContext.getMetadata()) : null)
                            .version(1L)
                            .createdAt(LocalDateTime.now())
                            .updatedAt(LocalDateTime.now())
                            .compressed(false)
                            .accessCount(0L)
                            .priority(sourceContext.getPriority())
                            .readOnly(false)
                            .build();
                    return copy;
                })
                .flatMap(this::saveContext);
    }

    @Override
    public Flux<ContextData> searchContexts(Map<String, Object> query, ContextType type) {
        return storageService.search(query, type)
                .flatMap(compressionService::decompress);
    }

    @Override
    public Flux<ContextData> getContextHistory(String contextId, ContextType type) {
        return storageService.findHistoryById(contextId, type)
                .flatMap(compressionService::decompress);
    }

    /**
     * 根据上下文类型获取TTL
     */
    private Duration getTtlByType(ContextType type) {
        return switch (type) {
            case SESSION -> Duration.ofMinutes(30);
            case AGENT -> Duration.ofHours(1);
            case TOOL -> Duration.ofMinutes(10);
            case KNOWLEDGE -> Duration.ofMinutes(15);
            case GLOBAL -> Duration.ofDays(30);
        };
    }
}
