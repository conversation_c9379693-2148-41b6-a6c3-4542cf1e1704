server:
  port: 8080

spring:
  application:
    name: agent-v3

  # <PERSON>配置
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: GMT+8
  
  # PostgreSQL 数据库配置
  datasource:
    url: *****************************************
    username: yilin
    password: agent_v2_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  # Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# LLM配置
llm:
  mock:
    enabled: false  # 使用真实的阿里云LLM服务

# AI模型配置
ai:
  model:
    provider: alibaba
    api-key: ${AI_API_KEY:sk-fc81d268ace64286a752e8ef084144cc}
    base-url: ${AI_BASE_URL:https://dashscope.aliyuncs.com/compatible-mode/v1}
    model-name: ${AI_MODEL_NAME:qwen-turbo}

# 阿里云通义千问配置
aliyun:
  dashscope:
    api-key: ${ALIYUN_API_KEY:sk-fc81d268ace64286a752e8ef084144cc}
    base-url: https://dashscope.aliyuncs.com/compatible-mode/v1
    model:
      chat: qwen-turbo
      embedding: text-embedding-v1
    timeout: 60000
    max-retries: 3

# Agent 配置
agent:
  modes:
    react:
      enabled: true
      max-iterations: 10
      timeout: 300000
    function-calling:
      enabled: true
      max-functions: 20
      timeout: 180000
  
  # 知识库配置
  knowledge:
    vector-store:
      dimension: 1536
      similarity-threshold: 0.7
    chunk-size: 1000
    chunk-overlap: 200

# 日志配置
logging:
  level:
    com.agent: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/agent-v3.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
