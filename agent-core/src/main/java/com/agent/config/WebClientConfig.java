package com.agent.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * WebClient配置
 */
@Configuration
@RequiredArgsConstructor
public class WebClientConfig {
    
    private final AliyunConfig aliyunConfig;
    
    @Bean
    public WebClient webClient() {
        return WebClient.builder()
                .baseUrl(aliyunConfig.getBaseUrl())
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
    }
}
