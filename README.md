# Agent V4 - 分布式AI Agent平台

一个基于Spring Boot和React的现代化AI Agent平台，支持函数调用、知识库检索和MCP协议集成。

## 🏗️ **项目架构**

```
agent-v4/
├── agent-core/          # 核心业务模块
│   ├── src/            # 主要业务逻辑
│   └── pom.xml         # 核心模块依赖
├── agent-context/       # 上下文管理模块
│   ├── src/            # 分布式上下文管理
│   └── pom.xml         # 上下文模块依赖
├── frontend/           # React前端应用
│   ├── src/            # 前端源码
│   ├── public/         # 静态资源
│   └── package.json    # 前端依赖
├── pom.xml             # 父项目配置
└── start.sh            # 启动脚本
```

## 🎯 **核心功能**

### **Agent Core 模块**
- **React Agent** - 基于思维链的推理模式
- **Function Calling Agent** - 支持工具调用的智能代理
- **知识库集成** - 向量数据库和文档检索
- **MCP协议支持** - 分布式工具调用
- **多模型支持** - 阿里云通义千问等

### **Context 模块**
- **分布式上下文管理** - 多层次上下文存储
- **Redis集群缓存** - 高性能缓存支持
- **数据压缩优化** - GZIP压缩存储
- **分布式锁机制** - 并发安全保证
- **监控指标** - 性能和健康监控

### **前端应用**
- **React 18** - 现代化前端框架
- **响应式设计** - 适配多种设备
- **实时通信** - WebSocket支持
- **组件化架构** - 可复用UI组件

## 🚀 **快速开始**

### **环境要求**
- Java 17+
- Node.js 16+
- Maven 3.8+
- Redis 6.0+
- PostgreSQL 13+

### **1. 克隆项目**
```bash
git clone <repository-url>
cd agent-v4
```

### **2. 编译项目**
```bash
# 编译所有模块
mvn clean compile

# 打包项目
mvn clean package -DskipTests
```

### **3. 配置数据库**
```bash
# 创建PostgreSQL数据库
createdb agent_v4

# 配置Redis（如果需要）
redis-server
```

### **4. 启动应用**
```bash
# 使用启动脚本
./start.sh

# 或者手动启动后端
cd agent-core
mvn spring-boot:run

# 启动前端（新终端）
cd frontend
npm install
npm start
```

### **5. 访问应用**
- 前端界面: http://localhost:3000
- 后端API: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html

## ⚙️ **配置说明**

### **应用配置 (application.yml)**
```yaml
# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0

# Agent配置
agent:
  llm:
    provider: aliyun
    api-key: your_api_key
    model: qwen-plus
  
  # 上下文管理配置
  context:
    session-context:
      ttl: PT30M
    cache:
      key-prefix: "agent:context:"
```

### **环境变量**
```bash
# 必需的环境变量
export ALIYUN_API_KEY=your_aliyun_api_key
export DATABASE_URL=*****************************************
export REDIS_URL=redis://localhost:6379

# 可选的环境变量
export LOG_LEVEL=INFO
export SERVER_PORT=8080
```

## 📚 **模块详细说明**

### **Agent Core 模块**
核心业务逻辑模块，包含：
- Agent执行引擎
- 工具管理服务
- 知识库服务
- LLM集成服务
- MCP协议支持

详细文档: [README-core.md](README-core.md)

### **Agent Context 模块**
分布式上下文管理模块，提供：
- 多层次上下文存储
- 分布式缓存管理
- 数据压缩和优化
- 监控和统计

详细文档: [agent-context/README.md](agent-context/README.md)

## 🔧 **开发指南**

### **添加新的Agent类型**
1. 在 `agent-core/src/main/java/com/agent/service/agent/` 下创建新的执行器
2. 实现 `AgentExecutor` 接口
3. 在 `AgentService` 中注册新类型

### **扩展工具功能**
1. 在 `agent-core/src/main/java/com/agent/service/tool/` 下添加工具实现
2. 更新 `ToolDefinition` 配置
3. 在前端添加对应的UI组件

### **自定义上下文存储**
1. 实现 `ContextStorageService` 接口
2. 在 `ContextAutoConfiguration` 中配置
3. 添加相应的配置属性

## 🧪 **测试**

### **运行单元测试**
```bash
# 测试所有模块
mvn test

# 测试特定模块
mvn test -pl agent-core
mvn test -pl agent-context
```

### **运行集成测试**
```bash
# 需要启动Redis和PostgreSQL
mvn verify
```

### **前端测试**
```bash
cd frontend
npm test
```

## 📊 **监控和运维**

### **健康检查**
- 应用健康: http://localhost:8080/actuator/health
- 上下文健康: http://localhost:8080/actuator/health/context
- 数据库连接: http://localhost:8080/actuator/health/db

### **性能指标**
- Micrometer指标: http://localhost:8080/actuator/metrics
- 上下文统计: http://localhost:8080/actuator/metrics/context

### **日志配置**
```yaml
logging:
  level:
    com.agent: DEBUG
    org.springframework.data.redis: INFO
  file:
    name: logs/agent-v4.log
```

## 🤝 **贡献指南**

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 **支持**

如果您遇到问题或有疑问：

1. 查看 [Issues](../../issues) 页面
2. 阅读相关文档
3. 提交新的 Issue

## 🗺️ **路线图**

- [ ] 支持更多LLM提供商
- [ ] 实现Agent工作流编排
- [ ] 添加可视化监控面板
- [ ] 支持Kubernetes部署
- [ ] 实现多租户架构
