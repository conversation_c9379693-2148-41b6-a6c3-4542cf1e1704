package com.agent.context.compression;

import com.agent.context.model.ContextData;
import reactor.core.publisher.Mono;

/**
 * 上下文压缩服务接口
 */
public interface ContextCompressionService {

    /**
     * 压缩上下文数据
     *
     * @param contextData 原始上下文数据
     * @return 压缩后的上下文数据
     */
    Mono<ContextData> compress(ContextData contextData);

    /**
     * 解压缩上下文数据
     *
     * @param contextData 压缩的上下文数据
     * @return 解压缩后的上下文数据
     */
    Mono<ContextData> decompress(ContextData contextData);

    /**
     * 检查是否需要压缩
     *
     * @param contextData 上下文数据
     * @return 是否需要压缩
     */
    boolean shouldCompress(ContextData contextData);

    /**
     * 获取压缩算法名称
     *
     * @return 算法名称
     */
    String getAlgorithmName();

    /**
     * 获取压缩比率
     *
     * @param originalSize   原始大小
     * @param compressedSize 压缩后大小
     * @return 压缩比率
     */
    double getCompressionRatio(long originalSize, long compressedSize);
}
