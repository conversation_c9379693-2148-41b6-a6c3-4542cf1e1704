package com.agent.context.model;

/**
 * 上下文类型枚举
 */
public enum ContextType {
    
    /**
     * 会话上下文 - 短期记忆
     * 生命周期: 单次会话
     * 存储: Redis (TTL: 30分钟)
     */
    SESSION("session", "会话上下文"),
    
    /**
     * Agent上下文 - 中期记忆
     * 生命周期: Agent实例级别
     * 存储: PostgreSQL + Redis缓存
     */
    AGENT("agent", "Agent上下文"),
    
    /**
     * 全局上下文 - 长期记忆
     * 生命周期: 跨会话持久化
     * 存储: Elasticsearch
     */
    GLOBAL("global", "全局上下文"),
    
    /**
     * 工具上下文 - 工具调用相关
     * 生命周期: 工具调用期间
     * 存储: Redis (TTL: 10分钟)
     */
    TOOL("tool", "工具上下文"),
    
    /**
     * 知识库上下文 - 知识检索相关
     * 生命周期: 知识检索期间
     * 存储: Redis (TTL: 15分钟)
     */
    KNOWLEDGE("knowledge", "知识库上下文");
    
    private final String code;
    private final String description;
    
    ContextType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static ContextType fromCode(String code) {
        for (ContextType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown context type: " + code);
    }
}
