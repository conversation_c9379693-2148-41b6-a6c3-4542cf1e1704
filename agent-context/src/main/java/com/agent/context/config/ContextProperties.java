package com.agent.context.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;

/**
 * 上下文管理配置属性
 */
@Data
@ConfigurationProperties(prefix = "agent.context")
public class ContextProperties {

    /**
     * 会话上下文配置
     */
    private SessionContext sessionContext = new SessionContext();

    /**
     * Agent上下文配置
     */
    private AgentContext agentContext = new AgentContext();

    /**
     * 全局上下文配置
     */
    private GlobalContext globalContext = new GlobalContext();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 压缩配置
     */
    private Compression compression = new Compression();

    @Data
    public static class SessionContext {
        /**
         * 会话上下文TTL
         */
        private Duration ttl = Duration.ofMinutes(30);

        /**
         * 最大消息数量
         */
        private int maxMessages = 100;

        /**
         * 是否启用压缩
         */
        private boolean compressionEnabled = true;
    }

    @Data
    public static class AgentContext {
        /**
         * Agent上下文缓存TTL
         */
        private Duration cacheTtl = Duration.ofHours(1);

        /**
         * 是否启用预加载
         */
        private boolean preloadEnabled = true;

        /**
         * 预加载线程池大小
         */
        private int preloadThreadPoolSize = 5;
    }

    @Data
    public static class GlobalContext {
        /**
         * 是否启用Elasticsearch存储
         */
        private boolean elasticsearchEnabled = false;

        /**
         * Elasticsearch索引名称
         */
        private String elasticsearchIndex = "agent-context";

        /**
         * 批量写入大小
         */
        private int batchSize = 100;
    }

    @Data
    public static class Cache {
        /**
         * 缓存键前缀
         */
        private String keyPrefix = "agent:context:";

        /**
         * 分布式锁超时时间
         */
        private Duration lockTimeout = Duration.ofSeconds(30);

        /**
         * 缓存更新重试次数
         */
        private int retryAttempts = 3;
    }

    @Data
    public static class Compression {
        /**
         * 压缩阈值（字节）
         */
        private int threshold = 1024;

        /**
         * 压缩算法
         */
        private String algorithm = "gzip";

        /**
         * 压缩级别
         */
        private int level = 6;
    }
}
