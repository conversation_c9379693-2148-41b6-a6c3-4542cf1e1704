package com.agent.context.storage;

import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 上下文存储服务接口
 */
public interface ContextStorageService {

    /**
     * 保存上下文数据
     *
     * @param contextData 上下文数据
     * @return 保存后的上下文数据
     */
    Mono<ContextData> save(ContextData contextData);

    /**
     * 根据ID获取上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 上下文数据
     */
    Mono<ContextData> findById(String contextId, ContextType type);

    /**
     * 根据实体ID获取上下文数据
     *
     * @param entityId 实体ID
     * @param type     上下文类型
     * @return 上下文数据列表
     */
    Flux<ContextData> findByEntityId(String entityId, ContextType type);

    /**
     * 批量获取上下文数据
     *
     * @param contextIds 上下文ID列表
     * @param type       上下文类型
     * @return 上下文数据列表
     */
    Flux<ContextData> findByIds(List<String> contextIds, ContextType type);

    /**
     * 更新上下文数据
     *
     * @param contextData 上下文数据
     * @return 更新后的上下文数据
     */
    Mono<ContextData> update(ContextData contextData);

    /**
     * 删除上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 删除结果
     */
    Mono<Void> deleteById(String contextId, ContextType type);

    /**
     * 批量删除上下文数据
     *
     * @param contextIds 上下文ID列表
     * @param type       上下文类型
     * @return 删除结果
     */
    Mono<Void> deleteByIds(List<String> contextIds, ContextType type);

    /**
     * 检查上下文是否存在
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 是否存在
     */
    Mono<Boolean> existsById(String contextId, ContextType type);

    /**
     * 统计上下文数量
     *
     * @param type 上下文类型
     * @return 数量
     */
    Mono<Long> countByType(ContextType type);

    /**
     * 搜索上下文数据
     *
     * @param query 搜索条件
     * @param type  上下文类型
     * @return 搜索结果
     */
    Flux<ContextData> search(Map<String, Object> query, ContextType type);

    /**
     * 获取上下文历史版本
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 历史版本列表
     */
    Flux<ContextData> findHistoryById(String contextId, ContextType type);

    /**
     * 清理过期的上下文数据
     *
     * @param type 上下文类型
     * @return 清理的数量
     */
    Mono<Long> cleanupExpired(ContextType type);

    /**
     * 获取存储统计信息
     *
     * @param type 上下文类型
     * @return 统计信息
     */
    Mono<Map<String, Object>> getStorageStats(ContextType type);
}
