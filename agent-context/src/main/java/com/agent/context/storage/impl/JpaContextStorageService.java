package com.agent.context.storage.impl;

import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import com.agent.context.storage.ContextStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * JPA上下文存储服务实现
 * 注意：这是一个简化的内存实现，实际项目中应该使用真正的JPA Repository
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JpaContextStorageService implements ContextStorageService {

    // 简化实现：使用内存存储，实际应该使用JPA Repository
    private final Map<String, ContextData> storage = new ConcurrentHashMap<>();

    @Override
    public Mono<ContextData> save(ContextData contextData) {
        log.debug("保存上下文到存储: contextId={}", contextData.getContextId());
        
        return Mono.fromCallable(() -> {
            String key = buildKey(contextData.getContextId(), contextData.getType());
            
            // 设置时间戳
            LocalDateTime now = LocalDateTime.now();
            if (contextData.getCreatedAt() == null) {
                contextData.setCreatedAt(now);
            }
            contextData.setUpdatedAt(now);
            
            // 设置版本号
            if (contextData.getVersion() == null) {
                contextData.setVersion(1L);
            }
            
            storage.put(key, contextData);
            log.debug("上下文已保存到存储: key={}", key);
            
            return contextData;
        });
    }

    @Override
    public Mono<ContextData> findById(String contextId, ContextType type) {
        log.debug("从存储查找上下文: contextId={}, type={}", contextId, type);
        
        return Mono.fromCallable(() -> {
            String key = buildKey(contextId, type);
            ContextData contextData = storage.get(key);
            
            if (contextData != null && !contextData.isExpired()) {
                log.debug("从存储找到上下文: key={}", key);
                return contextData;
            }
            
            log.debug("存储中未找到上下文或已过期: key={}", key);
            return null;
        });
    }

    @Override
    public Flux<ContextData> findByEntityId(String entityId, ContextType type) {
        log.debug("根据实体ID查找上下文: entityId={}, type={}", entityId, type);
        
        return Flux.fromIterable(storage.values())
                .filter(contextData -> 
                    type.equals(contextData.getType()) && 
                    entityId.equals(contextData.getEntityId()) &&
                    !contextData.isExpired()
                );
    }

    @Override
    public Flux<ContextData> findByIds(List<String> contextIds, ContextType type) {
        log.debug("批量查找上下文: contextIds={}, type={}", contextIds, type);
        
        return Flux.fromIterable(contextIds)
                .flatMap(contextId -> findById(contextId, type))
                .filter(contextData -> contextData != null);
    }

    @Override
    public Mono<ContextData> update(ContextData contextData) {
        log.debug("更新存储中的上下文: contextId={}", contextData.getContextId());
        
        return Mono.fromCallable(() -> {
            String key = buildKey(contextData.getContextId(), contextData.getType());
            
            // 检查是否存在
            ContextData existing = storage.get(key);
            if (existing == null) {
                throw new RuntimeException("上下文不存在: " + contextData.getContextId());
            }
            
            // 更新时间戳和版本号
            contextData.setUpdatedAt(LocalDateTime.now());
            if (contextData.getVersion() != null) {
                contextData.setVersion(contextData.getVersion() + 1);
            }
            
            storage.put(key, contextData);
            log.debug("上下文已更新: key={}", key);
            
            return contextData;
        });
    }

    @Override
    public Mono<Void> deleteById(String contextId, ContextType type) {
        log.debug("从存储删除上下文: contextId={}, type={}", contextId, type);
        
        return Mono.fromRunnable(() -> {
            String key = buildKey(contextId, type);
            ContextData removed = storage.remove(key);
            
            if (removed != null) {
                log.debug("上下文已从存储删除: key={}", key);
            } else {
                log.debug("存储中未找到要删除的上下文: key={}", key);
            }
        });
    }

    @Override
    public Mono<Void> deleteByIds(List<String> contextIds, ContextType type) {
        log.debug("批量删除上下文: contextIds={}, type={}", contextIds, type);
        
        return Flux.fromIterable(contextIds)
                .flatMap(contextId -> deleteById(contextId, type))
                .then();
    }

    @Override
    public Mono<Boolean> existsById(String contextId, ContextType type) {
        return Mono.fromCallable(() -> {
            String key = buildKey(contextId, type);
            ContextData contextData = storage.get(key);
            return contextData != null && !contextData.isExpired();
        });
    }

    @Override
    public Mono<Long> countByType(ContextType type) {
        return Mono.fromCallable(() -> 
            storage.values().stream()
                    .filter(contextData -> 
                        type.equals(contextData.getType()) && 
                        !contextData.isExpired()
                    )
                    .count()
        );
    }

    @Override
    public Flux<ContextData> search(Map<String, Object> query, ContextType type) {
        log.debug("搜索上下文: query={}, type={}", query, type);
        
        // 简化实现：基于内容匹配
        return Flux.fromIterable(storage.values())
                .filter(contextData -> 
                    type.equals(contextData.getType()) && 
                    !contextData.isExpired() &&
                    matchesQuery(contextData, query)
                );
    }

    @Override
    public Flux<ContextData> findHistoryById(String contextId, ContextType type) {
        log.debug("查找上下文历史: contextId={}, type={}", contextId, type);
        
        // 简化实现：返回当前版本
        return findById(contextId, type)
                .flux()
                .filter(contextData -> contextData != null);
    }

    @Override
    public Mono<Long> cleanupExpired(ContextType type) {
        log.info("清理过期的存储上下文: type={}", type);
        
        return Mono.fromCallable(() -> {
            long count = 0;
            var iterator = storage.entrySet().iterator();
            
            while (iterator.hasNext()) {
                var entry = iterator.next();
                ContextData contextData = entry.getValue();
                
                if (type.equals(contextData.getType()) && contextData.isExpired()) {
                    iterator.remove();
                    count++;
                    log.debug("清理过期上下文: key={}", entry.getKey());
                }
            }
            
            log.info("清理完成，删除了 {} 个过期上下文", count);
            return count;
        });
    }

    @Override
    public Mono<Map<String, Object>> getStorageStats(ContextType type) {
        return Mono.fromCallable(() -> {
            long totalCount = storage.values().stream()
                    .filter(contextData -> type.equals(contextData.getType()))
                    .count();
            
            long activeCount = storage.values().stream()
                    .filter(contextData -> 
                        type.equals(contextData.getType()) && 
                        !contextData.isExpired()
                    )
                    .count();
            
            long expiredCount = totalCount - activeCount;
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("type", type.getCode());
            stats.put("totalCount", totalCount);
            stats.put("activeCount", activeCount);
            stats.put("expiredCount", expiredCount);
            stats.put("storageType", "memory");
            
            return stats;
        });
    }

    /**
     * 构建存储键
     */
    private String buildKey(String contextId, ContextType type) {
        return type.getCode() + ":" + contextId;
    }

    /**
     * 检查上下文是否匹配查询条件
     */
    private boolean matchesQuery(ContextData contextData, Map<String, Object> query) {
        if (query == null || query.isEmpty()) {
            return true;
        }
        
        // 简化实现：检查内容中是否包含查询条件
        for (Map.Entry<String, Object> entry : query.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (contextData.getContent() != null && 
                contextData.getContent().containsKey(key)) {
                Object contextValue = contextData.getContent().get(key);
                if (!value.equals(contextValue)) {
                    return false;
                }
            } else {
                return false;
            }
        }
        
        return true;
    }
}
