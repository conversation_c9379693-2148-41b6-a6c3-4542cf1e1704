# Agent Context Module Configuration
agent:
  context:
    # 会话上下文配置
    session-context:
      ttl: PT30M  # 30分钟
      max-messages: 100
      compression-enabled: true
    
    # Agent上下文配置
    agent-context:
      cache-ttl: PT1H  # 1小时
      preload-enabled: true
      preload-thread-pool-size: 5
    
    # 全局上下文配置
    global-context:
      elasticsearch-enabled: false
      elasticsearch-index: agent-context
      batch-size: 100
    
    # 缓存配置
    cache:
      key-prefix: "agent:context:"
      lock-timeout: PT30S  # 30秒
      retry-attempts: 3
    
    # 压缩配置
    compression:
      threshold: 1024  # 1KB
      algorithm: gzip
      level: 6

# Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

# Redisson配置
redisson:
  config: |
    singleServerConfig:
      address: "redis://localhost:6379"
      database: 0
      connectionPoolSize: 10
      connectionMinimumIdleSize: 5
      timeout: 3000
      retryAttempts: 3
      retryInterval: 1500

# Elasticsearch配置（可选）
#spring:
#  elasticsearch:
#    uris: http://localhost:9200
#    username: 
#    password: 
#    connection-timeout: 1s
#    socket-timeout: 30s

# 日志配置
logging:
  level:
    com.agent.context: DEBUG
    org.springframework.data.redis: INFO
    org.redisson: INFO
