export enum AgentMode {
  REACT = 'REACT',
  FUNCTION_CALLING = 'FUNCTION_CALLING'
}

export enum AgentStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

export interface ModelConfig {
  modelName: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  streamEnabled: boolean;
  stopSequences?: string;
}

export interface Agent {
  id?: number;
  name: string;
  description?: string;
  mode: AgentMode;
  systemPrompt: string;
  openingStatement?: string;
  modelConfig: ModelConfig;
  status: AgentStatus;
  creatorId: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface AgentExecutionRequest {
  query: string;
  conversationId?: string;
  userId: number;
  stream?: boolean;
  context?: Record<string, any>;
  maxExecutionTime?: number;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface ToolCall {
  toolName: string;
  parameters: string;
  result: string;
  success: boolean;
  error?: string;
  executionTime: number;
}

export interface KnowledgeResult {
  content: string;
  score: number;
  source: string;
  metadata?: Record<string, any>;
}

export interface AgentExecutionResponse {
  content: string;
  finished: boolean;
  isStream?: boolean;
  stepType?: string;
  reasoning?: string;
  toolCalls?: ToolCall[];
  knowledgeResults?: KnowledgeResult[];
  tokenUsage?: TokenUsage;
  error?: string;
  executionTime?: number;
  timestamp: string;
  metadata?: Record<string, any>;
}
