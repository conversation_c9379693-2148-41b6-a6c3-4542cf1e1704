package com.agent.service.agent;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.entity.Agent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * Agent服务接口
 */
public interface AgentService {
    
    /**
     * 创建Agent
     */
    Mono<Agent> createAgent(Agent agent);
    
    /**
     * 更新Agent
     */
    Mono<Agent> updateAgent(Agent agent);
    
    /**
     * 删除Agent
     */
    Mono<Void> deleteAgent(Long agentId);
    
    /**
     * 根据ID获取Agent
     */
    Mono<Agent> getAgentById(Long agentId);
    
    /**
     * 获取用户的Agent列表
     */
    Mono<List<Agent>> getAgentsByUserId(Long userId);
    
    /**
     * 发布Agent
     */
    Mono<Agent> publishAgent(Long agentId);
    
    /**
     * 归档Agent
     */
    Mono<Agent> archiveAgent(Long agentId);
    
    /**
     * 执行Agent (同步)
     */
    Mono<AgentExecutionResponse> executeAgent(AgentExecutionRequest request);
    
    /**
     * 执行Agent (流式)
     */
    Flux<AgentExecutionResponse> executeAgentStream(AgentExecutionRequest request);
    
    /**
     * 验证Agent配置
     */
    Mono<Boolean> validateAgent(Long agentId);
    
    /**
     * 复制Agent
     */
    Mono<Agent> cloneAgent(Long agentId, Long newOwnerId);
}
