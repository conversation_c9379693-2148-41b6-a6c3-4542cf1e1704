package com.agent.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Agent执行响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentExecutionResponse {
    
    /**
     * 响应内容
     */
    private String content;
    
    /**
     * 是否已完成
     */
    private Boolean finished;
    
    /**
     * 是否为流式响应
     */
    @Builder.Default
    private Boolean isStream = false;
    
    /**
     * 步骤类型 (thought, action, observation, function_call, final_answer)
     */
    private String stepType;
    
    /**
     * 推理过程 (仅ReAct模式)
     */
    private String reasoning;
    
    /**
     * 工具调用信息
     */
    private List<ToolCall> toolCalls;
    
    /**
     * 知识库检索结果
     */
    private List<KnowledgeResult> knowledgeResults;
    
    /**
     * Token使用统计
     */
    private TokenUsage tokenUsage;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;
    
    /**
     * 响应时间戳
     */
    @Builder.Default
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 工具调用信息
     */
    @Data
    @Builder
    public static class ToolCall {
        private String toolName;
        private String parameters;
        private String result;
        private Boolean success;
        private String error;
        private Long executionTime;
    }
    
    /**
     * 知识库检索结果
     */
    @Data
    @Builder
    public static class KnowledgeResult {
        private String content;
        private Double score;
        private String source;
        private Map<String, Object> metadata;
    }
    
    /**
     * Token使用统计
     */
    @Data
    @Builder
    public static class TokenUsage {
        private Integer promptTokens;
        private Integer completionTokens;
        private Integer totalTokens;
    }
}
