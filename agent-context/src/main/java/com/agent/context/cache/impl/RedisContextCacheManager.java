package com.agent.context.cache.impl;

import com.agent.context.cache.ContextCacheManager;
import com.agent.context.config.ContextProperties;
import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis上下文缓存管理器实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisContextCacheManager implements ContextCacheManager {

    private final ReactiveRedisTemplate<String, Object> redisTemplate;
    private final ReactiveStringRedisTemplate stringRedisTemplate;
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;
    private final ContextProperties contextProperties;

    @Override
    public Mono<ContextData> get(String contextId, ContextType type) {
        String key = buildKey(contextId, type);
        
        return redisTemplate.opsForValue().get(key)
                .cast(String.class)
                .flatMap(this::deserializeContextData)
                .doOnNext(contextData -> {
                    // 更新访问统计
                    contextData.incrementAccessCount();
                    // 异步更新访问统计到缓存
                    updateAccessStats(key, contextData).subscribe();
                })
                .doOnSuccess(contextData -> {
                    if (contextData != null) {
                        log.debug("缓存命中: key={}", key);
                    } else {
                        log.debug("缓存未命中: key={}", key);
                    }
                })
                .onErrorResume(throwable -> {
                    log.error("从缓存获取上下文失败: key={}", key, throwable);
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Void> put(ContextData contextData, Duration ttl) {
        String key = buildKey(contextData.getContextId(), contextData.getType());
        
        return serializeContextData(contextData)
                .flatMap(serialized -> {
                    if (ttl != null && !ttl.isZero()) {
                        return redisTemplate.opsForValue().set(key, serialized, ttl).then();
                    } else {
                        return redisTemplate.opsForValue().set(key, serialized).then();
                    }
                })
                .doOnSuccess(result -> log.debug("上下文已缓存: key={}, ttl={}", key, ttl))
                .onErrorResume(throwable -> {
                    log.error("缓存上下文失败: key={}", key, throwable);
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Void> delete(String contextId, ContextType type) {
        String key = buildKey(contextId, type);
        
        return redisTemplate.delete(key)
                .doOnNext(count -> log.debug("从缓存删除上下文: key={}, deleted={}", key, count))
                .then()
                .onErrorResume(throwable -> {
                    log.error("从缓存删除上下文失败: key={}", key, throwable);
                    return Mono.empty();
                });
    }

    @Override
    public Flux<ContextData> multiGet(List<String> contextIds, ContextType type) {
        List<String> keys = contextIds.stream()
                .map(id -> buildKey(id, type))
                .toList();
        
        return redisTemplate.opsForValue().multiGet(keys)
                .flatMapMany(Flux::fromIterable)
                .cast(String.class)
                .flatMap(this::deserializeContextData)
                .onErrorResume(throwable -> {
                    log.error("批量获取上下文失败: keys={}", keys, throwable);
                    return Flux.empty();
                });
    }

    @Override
    public Mono<Void> multiPut(List<ContextData> contextDataList, Duration ttl) {
        return Flux.fromIterable(contextDataList)
                .flatMap(contextData -> put(contextData, ttl))
                .then()
                .onErrorResume(throwable -> {
                    log.error("批量缓存上下文失败", throwable);
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Boolean> exists(String contextId, ContextType type) {
        String key = buildKey(contextId, type);
        
        return redisTemplate.hasKey(key)
                .onErrorReturn(false);
    }

    @Override
    public Mono<Void> expire(String contextId, ContextType type, Duration ttl) {
        String key = buildKey(contextId, type);
        
        return redisTemplate.expire(key, ttl)
                .doOnNext(result -> log.debug("设置上下文过期时间: key={}, ttl={}, result={}", key, ttl, result))
                .then()
                .onErrorResume(throwable -> {
                    log.error("设置上下文过期时间失败: key={}", key, throwable);
                    return Mono.empty();
                });
    }

    @Override
    public Mono<Duration> getTtl(String contextId, ContextType type) {
        String key = buildKey(contextId, type);
        
        return redisTemplate.getExpire(key)
                .onErrorReturn(Duration.ZERO);
    }

    @Override
    public Mono<Long> cleanupExpired(ContextType type) {
        String pattern = buildKeyPattern(type);
        
        return stringRedisTemplate.scan()
                .filter(key -> key.matches(pattern))
                .flatMap(key -> redisTemplate.hasKey(key)
                        .filter(exists -> !exists)
                        .map(exists -> key))
                .flatMap(redisTemplate::delete)
                .reduce(0L, Long::sum)
                .doOnNext(count -> log.info("清理过期上下文: type={}, count={}", type, count))
                .onErrorReturn(0L);
    }

    @Override
    public Mono<CacheStats> getStats(ContextType type) {
        // 实现缓存统计信息获取
        return Mono.fromCallable(() -> (CacheStats) new RedisCacheStats())
                .onErrorReturn(new RedisCacheStats());
    }

    @Override
    public <T> Mono<T> withDistributedLock(String lockKey, Duration timeout, Supplier<Mono<T>> operation) {
        RLock lock = redissonClient.getLock(lockKey);
        
        return Mono.fromCallable(() -> {
            try {
                boolean acquired = lock.tryLock(timeout.toMillis(), TimeUnit.MILLISECONDS);
                if (!acquired) {
                    throw new RuntimeException("获取分布式锁失败: " + lockKey);
                }
                return acquired;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("获取分布式锁被中断: " + lockKey, e);
            }
        })
        .flatMap(acquired -> operation.get())
        .doFinally(signalType -> {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        });
    }

    @Override
    public Mono<Void> warmup(List<String> contextIds, ContextType type) {
        log.info("开始预热缓存: type={}, count={}", type, contextIds.size());
        
        return Flux.fromIterable(contextIds)
                .flatMap(contextId -> exists(contextId, type)
                        .filter(exists -> !exists)
                        .map(exists -> contextId))
                .collectList()
                .doOnNext(missingIds -> log.info("需要预热的上下文数量: {}", missingIds.size()))
                .then()
                .doOnSuccess(result -> log.info("缓存预热完成: type={}", type));
    }

    /**
     * 构建缓存键
     */
    private String buildKey(String contextId, ContextType type) {
        return contextProperties.getCache().getKeyPrefix() + type.getCode() + ":" + contextId;
    }

    /**
     * 构建键模式
     */
    private String buildKeyPattern(ContextType type) {
        return contextProperties.getCache().getKeyPrefix() + type.getCode() + ":*";
    }

    /**
     * 序列化上下文数据
     */
    private Mono<String> serializeContextData(ContextData contextData) {
        return Mono.fromCallable(() -> objectMapper.writeValueAsString(contextData))
                .onErrorMap(e -> new RuntimeException("序列化上下文数据失败", e));
    }

    /**
     * 反序列化上下文数据
     */
    private Mono<ContextData> deserializeContextData(String serialized) {
        return Mono.fromCallable(() -> objectMapper.readValue(serialized, ContextData.class))
                .onErrorMap(e -> new RuntimeException("反序列化上下文数据失败", e));
    }

    /**
     * 更新访问统计
     */
    private Mono<Void> updateAccessStats(String key, ContextData contextData) {
        return serializeContextData(contextData)
                .flatMap(serialized -> redisTemplate.opsForValue().set(key, serialized))
                .then()
                .onErrorResume(throwable -> {
                    log.warn("更新访问统计失败: key={}", key, throwable);
                    return Mono.empty();
                });
    }

    /**
     * Redis缓存统计实现
     */
    private static class RedisCacheStats implements CacheStats {
        @Override
        public long getHitCount() { return 0; }
        
        @Override
        public long getMissCount() { return 0; }
        
        @Override
        public double getHitRate() { return 0.0; }
        
        @Override
        public long getEvictionCount() { return 0; }
        
        @Override
        public long getSize() { return 0; }
        
        @Override
        public long getMemoryUsage() { return 0; }
    }
}
