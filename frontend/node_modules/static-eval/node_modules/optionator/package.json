{"name": "optionator", "version": "0.8.3", "author": "<PERSON> <<EMAIL>>", "description": "option parsing and help generation", "homepage": "https://github.com/gkz/optionator", "keywords": ["options", "flags", "option parsing", "cli"], "files": ["lib", "README.md", "LICENSE"], "main": "./lib/", "bugs": "https://github.com/gkz/optionator/issues", "license": "MIT", "engines": {"node": ">= 0.8.0"}, "repository": {"type": "git", "url": "git://github.com/gkz/optionator.git"}, "scripts": {"test": "make test"}, "dependencies": {"prelude-ls": "~1.1.2", "deep-is": "~0.1.3", "word-wrap": "~1.2.3", "type-check": "~0.3.2", "levn": "~0.3.0", "fast-levenshtein": "~2.0.6"}, "devDependencies": {"livescript": "~1.6.0", "mocha": "~6.2.2", "istanbul": "~0.4.5"}}