package com.agent.controller;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.entity.Agent;
import com.agent.domain.entity.ModelConfig;
import com.agent.service.agent.AgentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import reactor.core.publisher.Mono;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AgentController.class)
class AgentControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @MockBean
    private AgentService agentService;
    
    @Test
    void testCreateAgent() throws Exception {
        // Given
        Agent agent = createTestAgent();
        Agent savedAgent = createTestAgent();
        savedAgent.setId(1L);
        
        when(agentService.createAgent(any(Agent.class))).thenReturn(Mono.just(savedAgent));
        
        // When & Then
        mockMvc.perform(post("/v1/agents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(agent)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("测试Agent"));
    }
    
    @Test
    void testGetAgent() throws Exception {
        // Given
        Agent agent = createTestAgent();
        agent.setId(1L);
        
        when(agentService.getAgentById(1L)).thenReturn(Mono.just(agent));
        
        // When & Then
        mockMvc.perform(get("/v1/agents/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("测试Agent"));
    }
    
    @Test
    void testExecuteAgent() throws Exception {
        // Given
        Agent agent = createTestAgent();
        agent.setId(1L);
        
        AgentExecutionRequest request = AgentExecutionRequest.builder()
                .query("你好")
                .userId(1L)
                .build();
        
        AgentExecutionResponse response = AgentExecutionResponse.builder()
                .content("你好！我是AI助手")
                .finished(true)
                .build();
        
        when(agentService.getAgentById(1L)).thenReturn(Mono.just(agent));
        when(agentService.executeAgent(any(AgentExecutionRequest.class))).thenReturn(Mono.just(response));
        
        // When & Then
        mockMvc.perform(post("/v1/agents/1/execute")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").value("你好！我是AI助手"))
                .andExpect(jsonPath("$.finished").value(true));
    }
    
    private Agent createTestAgent() {
        Agent agent = new Agent();
        agent.setName("测试Agent");
        agent.setDescription("这是一个测试Agent");
        agent.setMode(Agent.AgentMode.FUNCTION_CALLING);
        agent.setSystemPrompt("你是一个测试助手");
        agent.setCreatorId(1L);
        
        ModelConfig modelConfig = new ModelConfig();
        modelConfig.setModelName("qwen-turbo");
        modelConfig.setTemperature(0.7);
        modelConfig.setMaxTokens(2000);
        modelConfig.setTopP(0.9);
        modelConfig.setStreamEnabled(true);
        
        agent.setModelConfig(modelConfig);
        
        return agent;
    }
}
