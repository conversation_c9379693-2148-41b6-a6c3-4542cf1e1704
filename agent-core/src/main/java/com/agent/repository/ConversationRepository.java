package com.agent.repository;

import com.agent.domain.entity.Conversation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 会话数据访问接口
 */
@Repository
public interface ConversationRepository extends JpaRepository<Conversation, Long> {
    
    /**
     * 根据UUID查找会话
     */
    Optional<Conversation> findByUuid(String uuid);
    
    /**
     * 根据用户ID和Agent ID查找会话列表
     */
    List<Conversation> findByUserIdAndAgentIdOrderByLastActiveAtDesc(Long userId, Long agentId);
    
    /**
     * 根据用户ID查找会话列表
     */
    List<Conversation> findByUserIdOrderByLastActiveAtDesc(Long userId);
}
