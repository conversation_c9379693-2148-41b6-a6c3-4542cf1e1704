package com.agent.service.llm;

import com.agent.config.AliyunConfig;
import com.agent.domain.dto.ChatRequest;
import com.agent.domain.dto.ChatResponse;
import com.agent.domain.dto.EmbeddingRequest;
import com.agent.domain.dto.EmbeddingResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阿里云通义千问服务实现
 */
@Service
@ConditionalOnProperty(name = "llm.mock.enabled", havingValue = "false")
@Primary
@RequiredArgsConstructor
@Slf4j
public class AliyunLLMService implements LLMService {
    
    private final AliyunConfig aliyunConfig;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    @Override
    @SuppressWarnings("unchecked")
    public Mono<ChatResponse> chat(ChatRequest request) {
        log.debug("发起聊天请求: {}", request);
        
        Map<String, Object> requestBody = buildChatRequestBody(request, false);
        
        return webClient.post()
                .uri("/chat/completions")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + aliyunConfig.getApiKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(Map.class)
                .map(this::parseChatResponse)
                .timeout(Duration.ofMillis(aliyunConfig.getTimeout()))
                .doOnError(error -> log.error("聊天请求失败", error))
                .retry(aliyunConfig.getMaxRetries());
    }
    
    @Override
    public Flux<ChatResponse> chatStream(ChatRequest request) {
        log.info("发起流式聊天请求: model={}, messages={}", request.getModel(), request.getMessages().size());
        log.debug("完整请求: {}", request);

        Map<String, Object> requestBody = buildChatRequestBody(request, true);
        log.info("请求体: {}", requestBody);
        log.info("API Key: {}...", aliyunConfig.getApiKey().substring(0, Math.min(10, aliyunConfig.getApiKey().length())));
        log.info("Base URL: {}", aliyunConfig.getBaseUrl());

        return webClient.post()
                .uri("/chat/completions")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + aliyunConfig.getApiKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(HttpHeaders.ACCEPT, "text/event-stream")
                .bodyValue(requestBody)
                .retrieve()
                .onStatus(status -> !status.is2xxSuccessful(),
                    response -> {
                        log.error("HTTP错误: {}", response.statusCode());
                        return response.bodyToMono(String.class)
                                .doOnNext(body -> log.error("错误响应体: {}", body))
                                .then(Mono.error(new RuntimeException("HTTP错误: " + response.statusCode())));
                    })
                .bodyToFlux(String.class)
                .doOnNext(line -> log.debug("收到SSE行: {}", line))
                .filter(line -> {
                    boolean isValidData = !line.equals("[DONE]") && !line.trim().isEmpty();
                    log.debug("过滤SSE行: '{}', 有效: {}", line, isValidData);
                    return isValidData;
                })
                .map(line -> {
                    log.debug("处理JSON数据: '{}'", line);
                    return line;
                })
                .map(this::parseStreamResponse)
                .filter(response -> response != null)  // 只过滤null响应，保留工具调用响应
                .timeout(Duration.ofMillis(aliyunConfig.getTimeout()))
                .doOnError(error -> log.error("流式聊天请求失败", error))
                .retry(aliyunConfig.getMaxRetries());
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public Mono<EmbeddingResponse> embedding(EmbeddingRequest request) {
        log.debug("发起嵌入向量请求: {}", request);
        
        Map<String, Object> requestBody = buildEmbeddingRequestBody(request);
        
        return webClient.post()
                .uri("/embeddings")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + aliyunConfig.getApiKey())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(Map.class)
                .map(this::parseEmbeddingResponse)
                .timeout(Duration.ofMillis(aliyunConfig.getTimeout()))
                .doOnError(error -> log.error("嵌入向量请求失败", error))
                .retry(aliyunConfig.getMaxRetries());
    }
    
    @Override
    public Mono<EmbeddingResponse> batchEmbedding(EmbeddingRequest request) {
        // 对于批量请求，可以分批处理或直接调用
        return embedding(request);
    }
    
    @Override
    public Mono<Boolean> isModelAvailable(String modelName) {
        return getSupportedModels()
                .map(models -> models.contains(modelName))
                .onErrorReturn(false);
    }
    
    @Override
    public Mono<List<String>> getSupportedModels() {
        // 返回阿里云支持的模型列表
        return Mono.just(List.of(
                "qwen-turbo",
                "qwen-plus",
                "qwen-max",
                "qwen-max-1201",
                "qwen-max-longcontext"
        ));
    }
    
    private Map<String, Object> buildChatRequestBody(ChatRequest request, boolean stream) {
        Map<String, Object> body = new HashMap<>();
        body.put("model", request.getModel());

        // 处理消息格式，确保兼容阿里云API
        List<Map<String, Object>> processedMessages = processMessagesForAliyun(request.getMessages());
        body.put("messages", processedMessages);
        body.put("stream", stream);
        
        if (request.getTemperature() != null) {
            body.put("temperature", request.getTemperature());
        }
        if (request.getMaxTokens() != null) {
            body.put("max_tokens", request.getMaxTokens());
        }
        if (request.getTopP() != null) {
            body.put("top_p", request.getTopP());
        }
        if (request.getFrequencyPenalty() != null) {
            body.put("frequency_penalty", request.getFrequencyPenalty());
        }
        if (request.getPresencePenalty() != null) {
            body.put("presence_penalty", request.getPresencePenalty());
        }
        if (request.getStop() != null && !request.getStop().isEmpty()) {
            body.put("stop", request.getStop());
        }
        if (request.getTools() != null && !request.getTools().isEmpty()) {
            body.put("tools", request.getTools());
            body.put("tool_choice", "auto");
        }
        
        return body;
    }

    /**
     * 处理消息格式以兼容阿里云API
     */
    private List<Map<String, Object>> processMessagesForAliyun(List<ChatRequest.Message> messages) {
        List<Map<String, Object>> processedMessages = new ArrayList<>();

        for (ChatRequest.Message message : messages) {
            Map<String, Object> processedMessage = new HashMap<>();
            processedMessage.put("role", message.getRole());

            // 处理不同类型的消息
            if ("tool".equals(message.getRole())) {
                // 工具调用结果消息，转换为用户消息格式
                processedMessage.put("role", "user");
                processedMessage.put("content", "工具执行结果: " + message.getContent());
            } else if (message.getToolCalls() != null && !message.getToolCalls().isEmpty()) {
                // 包含工具调用的助手消息
                processedMessage.put("content", message.getContent() != null ? message.getContent() : "");

                // 阿里云API可能不支持tool_calls字段，我们将工具调用信息包含在content中
                StringBuilder contentBuilder = new StringBuilder();
                if (message.getContent() != null && !message.getContent().isEmpty()) {
                    contentBuilder.append(message.getContent());
                }

                // 不添加tool_calls字段，避免API错误
                processedMessage.put("content", contentBuilder.toString());
            } else {
                // 普通消息
                processedMessage.put("content", message.getContent());
            }

            processedMessages.add(processedMessage);
        }

        return processedMessages;
    }

    private Map<String, Object> buildEmbeddingRequestBody(EmbeddingRequest request) {
        Map<String, Object> body = new HashMap<>();
        body.put("model", request.getModel());
        body.put("input", request.getInput());
        
        if (request.getInputType() != null) {
            body.put("input_type", request.getInputType());
        }
        
        return body;
    }
    
    private ChatResponse parseChatResponse(Map<String, Object> response) {
        try {
            // 解析阿里云API响应格式
            ChatResponse chatResponse = new ChatResponse();
            chatResponse.setId((String) response.get("id"));
            chatResponse.setModel((String) response.get("model"));
            if (response.get("created") != null) {
                chatResponse.setCreated(((Number) response.get("created")).longValue());
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> choices = (List<Map<String, Object>>) response.get("choices");
            if (choices != null && !choices.isEmpty()) {
                Map<String, Object> choice = choices.get(0);

                // 处理流式响应的delta字段
                @SuppressWarnings("unchecked")
                Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                if (delta != null) {
                    chatResponse.setContent((String) delta.get("content"));
                    chatResponse.setRole((String) delta.get("role"));

                    // 处理工具调用
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> toolCalls = (List<Map<String, Object>>) delta.get("tool_calls");
                    if (toolCalls != null) {
                        chatResponse.setToolCalls(toolCalls);
                    }
                } else {
                    // 处理非流式响应的message字段
                    @SuppressWarnings("unchecked")
                    Map<String, Object> message = (Map<String, Object>) choice.get("message");
                    if (message != null) {
                        chatResponse.setContent((String) message.get("content"));
                        chatResponse.setRole((String) message.get("role"));

                        // 处理工具调用
                        @SuppressWarnings("unchecked")
                        List<Map<String, Object>> toolCalls = (List<Map<String, Object>>) message.get("tool_calls");
                        if (toolCalls != null) {
                            chatResponse.setToolCalls(toolCalls);
                        }
                    }
                }

                chatResponse.setFinishReason((String) choice.get("finish_reason"));
            }
            
            // 处理使用统计
            @SuppressWarnings("unchecked")
            Map<String, Object> usage = (Map<String, Object>) response.get("usage");
            if (usage != null) {
                ChatResponse.Usage responseUsage = new ChatResponse.Usage();
                responseUsage.setPromptTokens(((Number) usage.get("prompt_tokens")).intValue());
                responseUsage.setCompletionTokens(((Number) usage.get("completion_tokens")).intValue());
                responseUsage.setTotalTokens(((Number) usage.get("total_tokens")).intValue());
                chatResponse.setUsage(responseUsage);
            }
            
            return chatResponse;
        } catch (Exception e) {
            log.error("解析聊天响应失败", e);
            throw new RuntimeException("解析聊天响应失败", e);
        }
    }
    
    private ChatResponse parseStreamResponse(String jsonLine) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> response = objectMapper.readValue(jsonLine, Map.class);
            ChatResponse chatResponse = parseChatResponse(response);
            log.debug("解析流式响应成功: content='{}', finishReason={}", chatResponse.getContent(), chatResponse.getFinishReason());
            return chatResponse;
        } catch (Exception e) {
            log.error("解析流式响应失败: {}", jsonLine, e);
            ChatResponse emptyResponse = new ChatResponse();
            emptyResponse.setContent(""); // 设置空字符串而不是null
            return emptyResponse;
        }
    }
    
    private EmbeddingResponse parseEmbeddingResponse(Map<String, Object> response) {
        try {
            EmbeddingResponse embeddingResponse = new EmbeddingResponse();
            embeddingResponse.setModel((String) response.get("model"));
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> data = (List<Map<String, Object>>) response.get("data");
            if (data != null && !data.isEmpty()) {
                Map<String, Object> embedding = data.get(0);
                @SuppressWarnings("unchecked")
                List<Double> vector = (List<Double>) embedding.get("embedding");
                embeddingResponse.setEmbedding(vector);
                embeddingResponse.setIndex(((Number) embedding.get("index")).intValue());
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> usage = (Map<String, Object>) response.get("usage");
            if (usage != null) {
                EmbeddingResponse.Usage responseUsage = new EmbeddingResponse.Usage();
                responseUsage.setPromptTokens(((Number) usage.get("prompt_tokens")).intValue());
                responseUsage.setTotalTokens(((Number) usage.get("total_tokens")).intValue());
                embeddingResponse.setUsage(responseUsage);
            }
            
            return embeddingResponse;
        } catch (Exception e) {
            log.error("解析嵌入向量响应失败", e);
            throw new RuntimeException("解析嵌入向量响应失败", e);
        }
    }
}
