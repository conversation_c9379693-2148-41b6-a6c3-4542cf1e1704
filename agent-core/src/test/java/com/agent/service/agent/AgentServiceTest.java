package com.agent.service.agent;

import com.agent.domain.entity.Agent;
import com.agent.domain.entity.ModelConfig;
import com.agent.repository.AgentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AgentServiceTest {
    
    @Mock
    private AgentRepository agentRepository;
    
    private AgentService agentService;
    
    @BeforeEach
    void setUp() {
        agentService = new AgentServiceImpl(agentRepository, new ArrayList<>());
    }
    
    @Test
    void testCreateAgent() {
        // Given
        Agent agent = createTestAgent();
        Agent savedAgent = createTestAgent();
        savedAgent.setId(1L);
        
        when(agentRepository.save(any(Agent.class))).thenReturn(savedAgent);
        
        // When & Then
        StepVerifier.create(agentService.createAgent(agent))
                .expectNextMatches(result -> 
                    result.getId().equals(1L) && 
                    result.getName().equals("测试Agent")
                )
                .verifyComplete();
    }
    
    @Test
    void testGetAgentById() {
        // Given
        Agent agent = createTestAgent();
        agent.setId(1L);
        
        when(agentRepository.findById(1L)).thenReturn(Optional.of(agent));
        
        // When & Then
        StepVerifier.create(agentService.getAgentById(1L))
                .expectNextMatches(result -> 
                    result.getId().equals(1L) && 
                    result.getName().equals("测试Agent")
                )
                .verifyComplete();
    }
    
    @Test
    void testGetAgentById_NotFound() {
        // Given
        when(agentRepository.findById(1L)).thenReturn(Optional.empty());
        
        // When & Then
        StepVerifier.create(agentService.getAgentById(1L))
                .expectError(IllegalArgumentException.class)
                .verify();
    }
    
    @Test
    void testUpdateAgent() {
        // Given
        Agent existingAgent = createTestAgent();
        existingAgent.setId(1L);
        
        Agent updatedAgent = createTestAgent();
        updatedAgent.setId(1L);
        updatedAgent.setName("更新后的Agent");
        
        when(agentRepository.findById(1L)).thenReturn(Optional.of(existingAgent));
        when(agentRepository.save(any(Agent.class))).thenReturn(updatedAgent);
        
        // When & Then
        StepVerifier.create(agentService.updateAgent(updatedAgent))
                .expectNextMatches(result -> 
                    result.getName().equals("更新后的Agent")
                )
                .verifyComplete();
    }
    
    private Agent createTestAgent() {
        Agent agent = new Agent();
        agent.setName("测试Agent");
        agent.setDescription("这是一个测试Agent");
        agent.setMode(Agent.AgentMode.FUNCTION_CALLING);
        agent.setSystemPrompt("你是一个测试助手");
        agent.setCreatorId(1L);
        
        ModelConfig modelConfig = new ModelConfig();
        modelConfig.setModelName("qwen-turbo");
        modelConfig.setTemperature(0.7);
        modelConfig.setMaxTokens(2000);
        modelConfig.setTopP(0.9);
        modelConfig.setStreamEnabled(true);
        
        agent.setModelConfig(modelConfig);
        
        return agent;
    }
}
