package com.agent.domain.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 工具实体
 */
@Entity
@Table(name = "tools")
@Data
@EqualsAndHashCode(callSuper = false)
public class Tool {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    /**
     * 工具类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ToolType type;
    
    /**
     * 工具分类
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ToolCategory category;
    
    /**
     * 工具图标URL
     */
    @Column(name = "icon_url", length = 500)
    private String iconUrl;
    
    /**
     * 工具参数定义 (JSON Schema格式)
     */
    @Column(name = "parameter_schema", columnDefinition = "TEXT")
    private String parameterSchema;
    
    /**
     * 工具实现类名
     */
    @Column(name = "implementation_class", length = 200)
    private String implementationClass;
    
    /**
     * API配置 (对于API类型的工具)
     */
    @Column(name = "api_config", columnDefinition = "TEXT")
    private String apiConfig;
    
    /**
     * 是否为内置工具
     */
    @Column(name = "is_builtin", nullable = false)
    private Boolean isBuiltin = false;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 创建者ID (内置工具为null)
     */
    @Column(name = "creator_id")
    private Long creatorId;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 工具类型枚举
     */
    public enum ToolType {
        BUILTIN("内置工具"),
        API("API工具"),
        CUSTOM("自定义工具"),
        WORKFLOW("工作流工具");
        
        private final String description;
        
        ToolType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 工具分类枚举
     */
    public enum ToolCategory {
        SEARCH("搜索"),
        CALCULATION("计算"),
        COMMUNICATION("通信"),
        FILE_PROCESSING("文件处理"),
        DATA_ANALYSIS("数据分析"),
        IMAGE_PROCESSING("图像处理"),
        TEXT_PROCESSING("文本处理"),
        WEB_SCRAPING("网页抓取"),
        DATABASE("数据库"),
        OTHER("其他");
        
        private final String description;
        
        ToolCategory(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
