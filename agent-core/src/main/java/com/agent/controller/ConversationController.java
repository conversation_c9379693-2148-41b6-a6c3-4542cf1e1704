package com.agent.controller;

import com.agent.domain.entity.Conversation;
import com.agent.domain.entity.Message;
import com.agent.service.conversation.ConversationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 会话控制器
 */
@RestController
@RequestMapping("/v1/conversations")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class ConversationController {
    
    private final ConversationService conversationService;
    
    /**
     * 获取会话详情
     */
    @GetMapping("/{conversationId}")
    public Mono<ResponseEntity<Conversation>> getConversation(@PathVariable String conversationId) {
        log.debug("获取会话详情: {}", conversationId);
        
        return conversationService.getOrCreateConversation(null, null, conversationId)
                .map(conversation -> ResponseEntity.ok(conversation))
                .doOnSuccess(response -> log.debug("获取会话详情成功: {}", conversationId));
    }
    
    /**
     * 获取会话消息历史
     */
    @GetMapping("/{conversationId}/messages")
    public Mono<ResponseEntity<List<Message>>> getConversationMessages(
            @PathVariable String conversationId,
            @RequestParam(defaultValue = "50") int limit) {
        
        log.debug("获取会话消息: conversationId={}, limit={}", conversationId, limit);
        
        return conversationService.getConversationHistory(conversationId, limit)
                .map(messages -> ResponseEntity.ok(messages))
                .doOnSuccess(response -> log.debug("获取会话消息成功: {} 条消息", response.getBody().size()));
    }
    
    /**
     * 删除会话
     */
    @DeleteMapping("/{conversationId}")
    public Mono<ResponseEntity<Void>> deleteConversation(@PathVariable String conversationId) {
        log.debug("删除会话: {}", conversationId);
        
        // TODO: 实现删除会话的逻辑
        return Mono.just(ResponseEntity.ok().build());
    }
}
