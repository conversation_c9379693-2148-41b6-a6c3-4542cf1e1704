<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全新Markdown聊天 - 2025.08.04</title>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2196f3, #21cbf3);
            color: white;
            padding: 25px;
            text-align: center;
        }
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .status-bar {
            background: #f8f9fa;
            padding: 15px 25px;
            border-bottom: 1px solid #e9ecef;
            font-family: monospace;
            font-size: 14px;
        }
        .chat-container {
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        .message.user {
            flex-direction: row-reverse;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }
        .message.user .avatar {
            background: #2196f3;
        }
        .message.assistant .avatar {
            background: #4caf50;
        }
        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 18px;
            position: relative;
        }
        .message.user .message-content {
            background: #2196f3;
            color: white;
        }
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .message.assistant.streaming .message-content {
            border-left: 4px solid #4caf50;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        .input-area {
            padding: 25px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        .input-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .input-group input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        .input-group input:focus {
            border-color: #2196f3;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #2196f3;
            color: white;
        }
        .btn-primary:hover {
            background: #1976d2;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .examples {
            background: #fff3e0;
            margin: 20px;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #ff9800;
        }
        .examples h3 {
            color: #f57c00;
            margin-bottom: 15px;
        }
        .example-item {
            background: white;
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid #e0e0e0;
        }
        .example-item:hover {
            background: #f5f5f5;
            transform: translateX(5px);
        }
        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3 {
            margin: 15px 0 10px 0;
            color: #333;
        }
        .message-content h1 {
            font-size: 1.5em;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }
        .message-content h2 {
            font-size: 1.3em;
            color: #2c3e50;
        }
        .message-content h3 {
            font-size: 1.1em;
            color: #34495e;
        }
        .message-content ul, .message-content ol {
            margin: 12px 0;
            padding-left: 25px;
        }
        .message-content li {
            margin: 6px 0;
            line-height: 1.5;
        }
        .message-content strong {
            color: #2c3e50;
            font-weight: 700;
        }
        .message-content p {
            margin: 10px 0;
            line-height: 1.6;
        }
        .message-content code {
            background: #f8f8f8;
            padding: 3px 8px;
            border-radius: 5px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
            color: #e91e63;
        }
        .message-content pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }
        .message-content blockquote {
            border-left: 4px solid #2196f3;
            padding-left: 15px;
            margin: 15px 0;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 全新Markdown聊天</h1>
            <p>纯Markdown解析 · 无JSON处理 · 实时渲染</p>
        </div>
        
        <div class="status-bar" id="statusBar">
            🔄 初始化中... | Marked库: <span id="markedStatus">检查中...</span>
        </div>
        
        <div class="examples">
            <h3>💡 快速测试</h3>
            <div class="example-item" onclick="sendExample('请用markdown格式介绍**人工智能**的发展历史')">
                📚 介绍人工智能（包含粗体）
            </div>
            <div class="example-item" onclick="sendExample('用markdown写一个关于编程的文章，包含# 标题、## 子标题和- 列表')">
                📝 编程文章（标题+列表）
            </div>
            <div class="example-item" onclick="sendExample('解释什么是机器学习，用### 三级标题分段')">
                🤖 机器学习解释（三级标题）
            </div>
        </div>
        
        <div class="chat-container" id="chatContainer">
            <div class="message assistant">
                <div class="avatar">🤖</div>
                <div class="message-content">
                    <strong>欢迎使用全新Markdown聊天！</strong><br><br>
                    我现在支持完整的Markdown格式渲染：<br>
                    • <strong>粗体文本</strong><br>
                    • <code>代码块</code><br>
                    • 标题和列表<br><br>
                    点击上方示例或直接输入消息开始对话吧！
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="输入你的消息..." />
                <button class="btn btn-primary" onclick="sendMessage()" id="sendBtn">发送</button>
                <button class="btn btn-secondary" onclick="clearChat()">清空</button>
            </div>
        </div>
    </div>

    <script>
        const AGENT_ID = 7;
        let isLoading = false;

        // 页面加载完成
        window.addEventListener('load', function() {
            checkMarkedLibrary();
            document.getElementById('messageInput').focus();
        });

        // 检查Marked库状态
        function checkMarkedLibrary() {
            const markedStatus = document.getElementById('markedStatus');
            const statusBar = document.getElementById('statusBar');
            
            if (typeof marked !== 'undefined' && marked.parse) {
                markedStatus.innerHTML = '<span style="color: #4caf50;">✅ 已加载</span>';
                statusBar.innerHTML = '✅ 系统就绪 | Marked库: <span style="color: #4caf50;">✅ 已加载</span>';
                
                // 测试Marked库
                try {
                    const testResult = marked.parse('**测试成功**');
                    console.log('✅ Marked库测试成功:', testResult);
                } catch (e) {
                    console.error('❌ Marked库测试失败:', e);
                }
            } else {
                markedStatus.innerHTML = '<span style="color: #f44336;">❌ 加载失败</span>';
                statusBar.innerHTML = '❌ Marked库加载失败 | 将使用纯文本模式';
            }
        }

        // 发送示例消息
        function sendExample(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            setLoading(true);
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            
            // 添加AI消息占位符
            const assistantMsg = addMessage('assistant', '', true);
            
            try {
                await processStream(message, assistantMsg);
            } catch (error) {
                console.error('❌ 发送失败:', error);
                updateMessageContent(assistantMsg, '❌ 发送失败: ' + error.message, false);
                updateStatus('❌ 发送失败: ' + error.message);
            } finally {
                setLoading(false);
            }
        }

        // 处理流式响应 - 纯Markdown，绝对无JSON解析
        async function processStream(message, assistantMsg) {
            updateStatus('🚀 发送请求...');
            
            const response = await fetch(`/v1/agents/${AGENT_ID}/execute/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify({
                    query: message,
                    userId: 1,
                    stream: true
                })
            });

            updateStatus(`📡 响应: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            updateStatus('📥 接收数据流...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    updateStatus('✅ 接收完成');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                console.log('📦 数据块:', chunk);
                
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        const data = line.slice(5); // 移除'data:'前缀
                        console.log('📄 纯文本数据:', data);
                        
                        if (data === '[DONE]') {
                            console.log('🏁 流结束');
                            updateMessageContent(assistantMsg, fullContent, false);
                            updateStatus('🎉 对话完成！');
                            return;
                        }
                        
                        // 纯Markdown文本处理 - 绝对不使用JSON.parse
                        if (data && data.trim() !== '') {
                            fullContent += data;
                            updateMessageContent(assistantMsg, fullContent, true);
                            updateStatus(`📝 接收中... ${fullContent.length}字符`);
                        }
                    }
                }
            }
            
            updateMessageContent(assistantMsg, fullContent, false);
            updateStatus('✅ 对话完成！');
        }

        // 更新消息内容并渲染Markdown
        function updateMessageContent(msgElement, content, streaming) {
            const contentDiv = msgElement.querySelector('.message-content');
            
            if (typeof marked !== 'undefined' && marked.parse) {
                try {
                    const rendered = marked.parse(content);
                    contentDiv.innerHTML = rendered;
                    console.log('✅ Markdown渲染成功');
                } catch (error) {
                    console.error('❌ Markdown渲染失败:', error);
                    contentDiv.textContent = content;
                }
            } else {
                contentDiv.textContent = content;
                console.warn('⚠️ 使用纯文本模式');
            }
            
            if (!streaming) {
                msgElement.classList.remove('streaming');
            }
            
            scrollToBottom();
        }

        // 添加消息
        function addMessage(role, content, streaming = false) {
            const container = document.getElementById('chatContainer');
            const msgDiv = document.createElement('div');
            msgDiv.className = `message ${role}${streaming ? ' streaming' : ''}`;
            
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.textContent = role === 'user' ? '👤' : '🤖';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            msgDiv.appendChild(avatar);
            msgDiv.appendChild(contentDiv);
            container.appendChild(msgDiv);
            
            scrollToBottom();
            return msgDiv;
        }

        // 更新状态
        function updateStatus(text) {
            document.getElementById('statusBar').innerHTML = text;
            console.log('📊', text);
        }

        // 设置加载状态
        function setLoading(loading) {
            isLoading = loading;
            const sendBtn = document.getElementById('sendBtn');
            const input = document.getElementById('messageInput');
            
            sendBtn.disabled = loading;
            sendBtn.textContent = loading ? '发送中...' : '发送';
            
            if (!loading) {
                input.focus();
            }
        }

        // 清空聊天
        function clearChat() {
            const container = document.getElementById('chatContainer');
            container.innerHTML = `
                <div class="message assistant">
                    <div class="avatar">🤖</div>
                    <div class="message-content">
                        聊天已清空，可以开始新的对话了！
                    </div>
                </div>
            `;
            updateStatus('🗑️ 聊天已清空');
        }

        // 滚动到底部
        function scrollToBottom() {
            const container = document.getElementById('chatContainer');
            container.scrollTop = container.scrollHeight;
        }

        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isLoading) {
                sendMessage();
            }
        });
    </script>
</body>
</html>
