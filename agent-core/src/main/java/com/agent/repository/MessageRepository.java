package com.agent.repository;

import com.agent.domain.entity.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 消息数据访问接口
 */
@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {
    
    /**
     * 根据会话UUID查找消息列表，按创建时间升序
     */
    @Query("SELECT m FROM Message m WHERE m.conversation.uuid = :conversationUuid ORDER BY m.createdAt ASC")
    List<Message> findByConversationUuidOrderByCreatedAtAsc(@Param("conversationUuid") String conversationUuid);
    
    /**
     * 根据会话UUID查找最近的消息列表
     */
    @Query("SELECT m FROM Message m WHERE m.conversation.uuid = :conversationUuid ORDER BY m.createdAt DESC")
    List<Message> findByConversationUuidOrderByCreatedAtDesc(@Param("conversationUuid") String conversationUuid);
    
    /**
     * 根据会话ID统计消息数量
     */
    @Query("SELECT COUNT(m) FROM Message m WHERE m.conversation.id = :conversationId")
    Long countByConversationId(@Param("conversationId") Long conversationId);
}
