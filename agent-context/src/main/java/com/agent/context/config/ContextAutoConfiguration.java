package com.agent.context.config;

import com.agent.context.service.ContextService;
import com.agent.context.service.impl.DistributedContextServiceImpl;
import com.agent.context.cache.ContextCacheManager;
import com.agent.context.cache.impl.RedisContextCacheManager;
import com.agent.context.storage.ContextStorageService;
import com.agent.context.storage.impl.JpaContextStorageService;
import com.agent.context.compression.ContextCompressionService;
import com.agent.context.compression.impl.GzipContextCompressionService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 上下文管理自动配置类
 */
@Configuration
@EnableConfigurationProperties(ContextProperties.class)
@ComponentScan(basePackages = "com.agent.context")
public class ContextAutoConfiguration {

    /**
     * 上下文服务
     */
    @Bean
    @ConditionalOnMissingBean
    public ContextService contextService(
            ContextCacheManager cacheManager,
            ContextStorageService storageService,
            ContextCompressionService compressionService) {
        return new DistributedContextServiceImpl(cacheManager, storageService, compressionService);
    }

    /**
     * 上下文缓存管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public ContextCacheManager contextCacheManager(
            org.springframework.data.redis.core.ReactiveRedisTemplate<String, Object> redisTemplate,
            org.springframework.data.redis.core.ReactiveStringRedisTemplate stringRedisTemplate,
            org.redisson.api.RedissonClient redissonClient,
            com.fasterxml.jackson.databind.ObjectMapper objectMapper,
            ContextProperties contextProperties) {
        return new RedisContextCacheManager(redisTemplate, stringRedisTemplate, redissonClient, objectMapper, contextProperties);
    }

    /**
     * 上下文存储服务
     */
    @Bean
    @ConditionalOnMissingBean
    public ContextStorageService contextStorageService() {
        return new JpaContextStorageService();
    }

    /**
     * 上下文压缩服务
     */
    @Bean
    @ConditionalOnMissingBean
    public ContextCompressionService contextCompressionService(
            com.fasterxml.jackson.databind.ObjectMapper objectMapper,
            ContextProperties contextProperties) {
        return new GzipContextCompressionService(objectMapper, contextProperties);
    }
}
