package com.agent.domain.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天响应DTO
 */
@Data
public class ChatResponse {
    
    /**
     * 响应ID
     */
    private String id;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 创建时间戳
     */
    private Long created;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 结束原因
     */
    private String finishReason;
    
    /**
     * 工具调用列表
     */
    private List<Map<String, Object>> toolCalls;
    
    /**
     * Token使用统计
     */
    private Usage usage;
    
    /**
     * 是否为流式响应的一部分
     */
    private Boolean isStream = false;
    
    /**
     * Token使用统计
     */
    @Data
    public static class Usage {
        /**
         * 提示词token数
         */
        private Integer promptTokens;
        
        /**
         * 完成token数
         */
        private Integer completionTokens;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
    }
}
