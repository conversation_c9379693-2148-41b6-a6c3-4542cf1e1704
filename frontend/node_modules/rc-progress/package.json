{"name": "rc-progress", "version": "4.0.0", "description": "progress ui component for react", "keywords": ["react", "react-component", "react-progress", "progress"], "homepage": "http://github.com/react-component/progress", "repository": {"type": "git", "url": "**************:react-component/progress.git"}, "bugs": {"url": "http://github.com/react-component/progress/issues"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es"], "scripts": {"start": "dumi dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d dist", "deploy": "npm run docs:build && npm run docs:deploy", "compile": "father build", "prepare": "dumi setup", "gh-pages": "npm run now-build && father doc deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "test": "rc-test", "tsc": "tsc --noEmit", "coverage": "father test --coverage", "now-build": "npm run docs:build"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.6", "rc-util": "^5.16.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/react": "^12.1.5", "@types/classnames": "^2.2.9", "@types/jest": "^29.4.0", "@types/keyv": "3.1.4", "@types/react": "^18.0.9", "@types/react-dom": "^18.0.3", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.0", "dumi": "^2.0.0", "enzyme": "^3.1.1", "enzyme-adapter-react-16": "^1.15.6", "enzyme-to-json": "^3.1.2", "eslint": "^8.57.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-unicorn": "^50.0.1", "father": "^4.0.0", "glob": "^8.0.1", "np": "^10.0.1", "prettier": "^3.1.1", "rc-test": "^7.0.15", "react": "^16.0.0", "react-dom": "^16.0.0", "typescript": "^5.0.0"}}