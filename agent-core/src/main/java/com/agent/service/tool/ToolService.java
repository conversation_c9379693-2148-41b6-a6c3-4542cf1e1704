package com.agent.service.tool;

import com.agent.domain.dto.ChatRequest;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 工具服务接口
 */
public interface ToolService {
    
    /**
     * 获取Agent可用的工具列表
     */
    Mono<List<ChatRequest.Tool>> getAvailableTools(Long agentId);
    
    /**
     * 获取工具描述文本 (用于ReAct模式)
     */
    String getAvailableToolsDescription(Long agentId);
    
    /**
     * 执行工具
     */
    Mono<String> executeTool(String toolName, String parameters);
    
    /**
     * 验证工具参数
     */
    Mono<Boolean> validateToolParameters(String toolName, String parameters);
    
    /**
     * 获取工具的参数Schema
     */
    Mono<String> getToolParameterSchema(String toolName);
}
