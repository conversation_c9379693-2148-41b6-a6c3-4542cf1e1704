package com.agent.domain.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Agent工具关联实体
 */
@Entity
@Table(name = "agent_tools")
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentTool {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的Agent
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private Agent agent;
    
    /**
     * 关联的工具
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tool_id", nullable = false)
    private Tool tool;
    
    /**
     * 工具配置 (JSON格式存储)
     */
    @Column(name = "tool_config", columnDefinition = "TEXT")
    private String toolConfig;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 执行顺序
     */
    @Column(name = "execution_order")
    private Integer executionOrder = 0;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
}
