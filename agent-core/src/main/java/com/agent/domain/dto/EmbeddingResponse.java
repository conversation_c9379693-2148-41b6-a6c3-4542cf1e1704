package com.agent.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 嵌入向量响应DTO
 */
@Data
public class EmbeddingResponse {
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 嵌入向量
     */
    private List<Double> embedding;
    
    /**
     * 索引
     */
    private Integer index;
    
    /**
     * Token使用统计
     */
    private Usage usage;
    
    /**
     * Token使用统计
     */
    @Data
    public static class Usage {
        /**
         * 提示词token数
         */
        private Integer promptTokens;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
    }
}
