package com.agent.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Data;

/**
 * 模型配置 - 嵌入到Agent实体中
 */
@Embeddable
@Data
public class ModelConfig {
    
    /**
     * 模型名称 (如: qwen-turbo, qwen-plus)
     */
    @Column(name = "model_name", length = 50)
    private String modelName = "qwen-turbo";
    
    /**
     * 温度参数 (0.0-2.0)
     */
    @Column(name = "temperature")
    private Double temperature = 0.7;
    
    /**
     * 最大输出token数
     */
    @Column(name = "max_tokens")
    private Integer maxTokens = 2000;
    
    /**
     * Top-p参数 (0.0-1.0)
     */
    @Column(name = "top_p")
    private Double topP = 0.9;
    
    /**
     * 频率惩罚 (-2.0-2.0)
     */
    @Column(name = "frequency_penalty")
    private Double frequencyPenalty = 0.0;
    
    /**
     * 存在惩罚 (-2.0-2.0)
     */
    @Column(name = "presence_penalty")
    private Double presencePenalty = 0.0;
    
    /**
     * 是否启用流式输出
     */
    @Column(name = "stream_enabled")
    private Boolean streamEnabled = true;
    
    /**
     * 停止词列表 (JSON格式存储)
     */
    @Column(name = "stop_sequences", columnDefinition = "TEXT")
    private String stopSequences;
}
