package com.agent.context.service;

import com.agent.context.model.ContextData;
import com.agent.context.model.ContextType;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 上下文服务接口
 */
public interface ContextService {

    /**
     * 获取上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 上下文数据
     */
    Mono<ContextData> getContext(String contextId, ContextType type);

    /**
     * 保存上下文数据
     *
     * @param contextData 上下文数据
     * @return 保存后的上下文数据
     */
    Mono<ContextData> saveContext(ContextData contextData);

    /**
     * 更新上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @param content   更新内容
     * @return 更新后的上下文数据
     */
    Mono<ContextData> updateContext(String contextId, ContextType type, Map<String, Object> content);

    /**
     * 删除上下文数据
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 删除结果
     */
    Mono<Void> deleteContext(String contextId, ContextType type);

    /**
     * 批量获取上下文数据
     *
     * @param contextIds 上下文ID列表
     * @param type       上下文类型
     * @return 上下文数据列表
     */
    Flux<ContextData> getContexts(List<String> contextIds, ContextType type);

    /**
     * 根据实体ID获取上下文数据
     *
     * @param entityId 实体ID
     * @param type     上下文类型
     * @return 上下文数据列表
     */
    Flux<ContextData> getContextsByEntity(String entityId, ContextType type);

    /**
     * 设置上下文过期时间
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @param ttl       过期时间
     * @return 操作结果
     */
    Mono<Void> setContextTtl(String contextId, ContextType type, Duration ttl);

    /**
     * 检查上下文是否存在
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 是否存在
     */
    Mono<Boolean> existsContext(String contextId, ContextType type);

    /**
     * 清理过期的上下文数据
     *
     * @param type 上下文类型
     * @return 清理的数量
     */
    Mono<Long> cleanupExpiredContexts(ContextType type);

    /**
     * 获取上下文统计信息
     *
     * @param type 上下文类型
     * @return 统计信息
     */
    Mono<Map<String, Object>> getContextStats(ContextType type);

    /**
     * 合并多个上下文
     *
     * @param contextIds 上下文ID列表
     * @param type       上下文类型
     * @return 合并后的上下文数据
     */
    Mono<ContextData> mergeContexts(List<String> contextIds, ContextType type);

    /**
     * 复制上下文
     *
     * @param sourceContextId 源上下文ID
     * @param targetContextId 目标上下文ID
     * @param type            上下文类型
     * @return 复制后的上下文数据
     */
    Mono<ContextData> copyContext(String sourceContextId, String targetContextId, ContextType type);

    /**
     * 搜索上下文
     *
     * @param query 搜索条件
     * @param type  上下文类型
     * @return 搜索结果
     */
    Flux<ContextData> searchContexts(Map<String, Object> query, ContextType type);

    /**
     * 获取上下文历史版本
     *
     * @param contextId 上下文ID
     * @param type      上下文类型
     * @return 历史版本列表
     */
    Flux<ContextData> getContextHistory(String contextId, ContextType type);
}
