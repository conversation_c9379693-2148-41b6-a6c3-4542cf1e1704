package com.agent.controller;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.entity.Agent;
import com.agent.domain.entity.Conversation;
import com.agent.service.agent.AgentService;
import com.agent.service.conversation.ConversationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Agent REST API控制器
 */
@RestController
@RequestMapping("/v1/agents")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AgentController {

    private final AgentService agentService;
    private final ConversationService conversationService;
    private final ObjectMapper objectMapper;
    
    /**
     * 创建Agent
     */
    @PostMapping
    public Mono<ResponseEntity<Agent>> createAgent(@Valid @RequestBody Agent agent) {
        log.info("创建Agent请求: {}", agent.getName());
        
        return agentService.createAgent(agent)
                .map(createdAgent -> ResponseEntity.ok(createdAgent))
                .doOnSuccess(response -> log.info("Agent创建成功: {}", response.getBody().getId()));
    }
    
    /**
     * 更新Agent
     */
    @PutMapping("/{agentId}")
    public Mono<ResponseEntity<Agent>> updateAgent(@PathVariable Long agentId, 
                                                  @Valid @RequestBody Agent agent) {
        log.info("更新Agent请求: {}", agentId);
        
        agent.setId(agentId);
        return agentService.updateAgent(agent)
                .map(updatedAgent -> ResponseEntity.ok(updatedAgent))
                .doOnSuccess(response -> log.info("Agent更新成功: {}", agentId));
    }
    
    /**
     * 删除Agent
     */
    @DeleteMapping("/{agentId}")
    public Mono<ResponseEntity<Void>> deleteAgent(@PathVariable Long agentId) {
        log.info("删除Agent请求: {}", agentId);
        
        return agentService.deleteAgent(agentId)
                .map(v -> ResponseEntity.ok().<Void>build())
                .doOnSuccess(response -> log.info("Agent删除成功: {}", agentId));
    }
    
    /**
     * 获取Agent详情
     */
    @GetMapping("/{agentId}")
    public Mono<ResponseEntity<Agent>> getAgent(@PathVariable Long agentId) {
        return agentService.getAgentById(agentId)
                .map(agent -> ResponseEntity.ok(agent));
    }
    
    /**
     * 获取用户的Agent列表
     */
    @GetMapping
    public Mono<ResponseEntity<List<Agent>>> getAgents(@RequestParam Long userId) {
        return agentService.getAgentsByUserId(userId)
                .map(agents -> ResponseEntity.ok(agents));
    }
    
    /**
     * 发布Agent
     */
    @PostMapping("/{agentId}/publish")
    public Mono<ResponseEntity<Agent>> publishAgent(@PathVariable Long agentId) {
        log.info("发布Agent请求: {}", agentId);
        
        return agentService.publishAgent(agentId)
                .map(publishedAgent -> ResponseEntity.ok(publishedAgent))
                .doOnSuccess(response -> log.info("Agent发布成功: {}", agentId));
    }
    
    /**
     * 归档Agent
     */
    @PostMapping("/{agentId}/archive")
    public Mono<ResponseEntity<Agent>> archiveAgent(@PathVariable Long agentId) {
        log.info("归档Agent请求: {}", agentId);
        
        return agentService.archiveAgent(agentId)
                .map(archivedAgent -> ResponseEntity.ok(archivedAgent))
                .doOnSuccess(response -> log.info("Agent归档成功: {}", agentId));
    }
    
    /**
     * 复制Agent
     */
    @PostMapping("/{agentId}/clone")
    public Mono<ResponseEntity<Agent>> cloneAgent(@PathVariable Long agentId, 
                                                 @RequestParam Long newOwnerId) {
        log.info("复制Agent请求: {} -> {}", agentId, newOwnerId);
        
        return agentService.cloneAgent(agentId, newOwnerId)
                .map(clonedAgent -> ResponseEntity.ok(clonedAgent))
                .doOnSuccess(response -> log.info("Agent复制成功: {} -> {}", 
                        agentId, response.getBody().getId()));
    }
    
    /**
     * 验证Agent配置
     */
    @PostMapping("/{agentId}/validate")
    public Mono<ResponseEntity<Boolean>> validateAgent(@PathVariable Long agentId) {
        return agentService.validateAgent(agentId)
                .map(isValid -> ResponseEntity.ok(isValid));
    }
    
    /**
     * 执行Agent (同步)
     */
    @PostMapping("/{agentId}/execute")
    public Mono<ResponseEntity<AgentExecutionResponse>> executeAgent(
            @PathVariable Long agentId,
            @Valid @RequestBody AgentExecutionRequest request) {

        log.info("执行Agent请求: {}", agentId);

        return agentService.getAgentById(agentId)
                .flatMap(agent -> {
                    // 创建或获取会话
                    return conversationService.getOrCreateConversation(agentId, request.getUserId(), request.getConversationId())
                            .flatMap(conversation -> {
                                // 保存用户消息
                                return conversationService.saveMessage(conversation.getUuid(), "user", request.getQuery())
                                        .then(Mono.defer(() -> {
                                            // 执行Agent
                                            AgentExecutionRequest executionRequest = request.toBuilder()
                                                    .agent(agent)
                                                    .conversationId(conversation.getUuid())
                                                    .build();

                                            return agentService.executeAgent(executionRequest);
                                        }))
                                        .flatMap(response -> {
                                            // 保存助手回复
                                            return conversationService.saveMessage(conversation.getUuid(), "assistant", response.getContent())
                                                    .thenReturn(response);
                                        });
                            });
                })
                .map(response -> ResponseEntity.ok(response))
                .doOnSuccess(response -> log.info("Agent执行完成: {}", agentId));
    }
    
    /**
     * 执行Agent (流式)
     */
    @PostMapping(value = "/{agentId}/execute/stream",
                produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> executeAgentStream(
            @PathVariable Long agentId,
            @Valid @RequestBody AgentExecutionRequest request) {

        log.info("流式执行Agent请求: {}", agentId);

        return agentService.getAgentById(agentId)
                .flatMapMany(agent -> {
                    // 创建或获取会话
                    return conversationService.getOrCreateConversation(agentId, request.getUserId(), request.getConversationId())
                            .flatMapMany(conversation -> {
                                // 保存用户消息
                                return conversationService.saveMessage(conversation.getUuid(), "user", request.getQuery())
                                        .thenMany(Flux.defer(() -> {
                                            // 执行Agent流式响应
                                            AgentExecutionRequest executionRequest = request.toBuilder()
                                                    .agent(agent)
                                                    .conversationId(conversation.getUuid())
                                                    .stream(true)
                                                    .build();

                                            // 收集所有流式内容
                                            StringBuilder assistantContent = new StringBuilder();

                                            return agentService.executeAgentStream(executionRequest)
                                                    .doOnNext(response -> {
                                                        // 收集AI回复内容
                                                        if (response.getContent() != null && !response.getContent().isEmpty()) {
                                                            assistantContent.append(response.getContent());
                                                        }
                                                    })
                                                    .doOnComplete(() -> {
                                                        // 流式完成后，保存完整的助手回复
                                                        String fullContent = assistantContent.toString();
                                                        if (!fullContent.isEmpty()) {
                                                            conversationService.saveMessage(conversation.getUuid(), "assistant", fullContent)
                                                                    .subscribe(
                                                                            savedMessage -> log.debug("保存助手回复成功: {}", savedMessage.getId()),
                                                                            error -> log.error("保存助手回复失败", error)
                                                                    );
                                                        }
                                                        log.debug("流式响应完成，会话: {}", conversation.getUuid());
                                                    });
                                        }));
                            });
                })
                .map(response -> {
                    // 返回纯Markdown格式内容
                    if (response.getContent() != null && !response.getContent().isEmpty()) {
                        return response.getContent();
                    } else if (response.getFinished() != null && response.getFinished()) {
                        // 流式结束标记
                        return "";
                    } else {
                        return "";
                    }
                })
                .filter(content -> content != null) // 过滤null内容
                .concatWith(Flux.just("[DONE]"))
                .doOnComplete(() -> log.info("Agent流式执行完成: {}", agentId));
    }

    /**
     * 获取Agent的会话列表
     */
    @GetMapping("/{agentId}/conversations")
    public Mono<ResponseEntity<List<Conversation>>> getAgentConversations(
            @PathVariable Long agentId,
            @RequestParam Long userId) {

        log.debug("获取Agent会话列表: agentId={}, userId={}", agentId, userId);

        return conversationService.getUserConversations(userId, agentId)
                .map(conversations -> ResponseEntity.ok(conversations))
                .doOnSuccess(response -> log.debug("获取会话列表成功: {} 个会话", response.getBody().size()));
    }
}
