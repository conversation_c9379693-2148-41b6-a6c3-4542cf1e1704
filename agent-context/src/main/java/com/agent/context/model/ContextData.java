package com.agent.context.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 上下文数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContextData {

    /**
     * 上下文ID
     */
    private String contextId;

    /**
     * 上下文类型
     */
    private ContextType type;

    /**
     * 关联的实体ID（如sessionId, agentId, userId等）
     */
    private String entityId;

    /**
     * 上下文内容（JSON格式）
     */
    private Map<String, Object> content;

    /**
     * 上下文元数据
     */
    private Map<String, Object> metadata;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 是否已压缩
     */
    private Boolean compressed;

    /**
     * 压缩算法
     */
    private String compressionAlgorithm;

    /**
     * 原始大小（字节）
     */
    private Long originalSize;

    /**
     * 压缩后大小（字节）
     */
    private Long compressedSize;

    /**
     * 访问次数
     */
    private Long accessCount;

    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessedAt;

    /**
     * 创建者ID
     */
    private String createdBy;

    /**
     * 更新者ID
     */
    private String updatedBy;

    /**
     * 标签（用于分类和检索）
     */
    private Map<String, String> tags;

    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;

    /**
     * 是否只读
     */
    private Boolean readOnly;

    /**
     * 检查是否过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 增加访问次数
     */
    public void incrementAccessCount() {
        this.accessCount = (this.accessCount == null ? 0 : this.accessCount) + 1;
        this.lastAccessedAt = LocalDateTime.now();
    }

    /**
     * 设置过期时间（从现在开始的相对时间）
     */
    public void setTtl(java.time.Duration duration) {
        this.expiresAt = LocalDateTime.now().plus(duration);
    }

    /**
     * 获取内容大小
     */
    public long getContentSize() {
        if (compressed != null && compressed) {
            return compressedSize != null ? compressedSize : 0;
        }
        return originalSize != null ? originalSize : 0;
    }
}
